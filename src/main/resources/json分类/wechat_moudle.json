{"source": "wechat", "mapping": {"交易类型": "微信", "交易方姓名": "用户ID", "交易方账户": "用户银行卡号", "交易方开户银行": "", "交易方身份证": "", "对手方姓名": "对手方ID", "对手方账户": "对手方银行卡号", "对手方开户银行": "", "对手方身份证": "", "交易流水号": "交易单号", "交易时间": "交易时间", "交易金额": "交易金额(元)", "交易余额": "账户余额(元)", "收付标志": "normalize_income_expense(借贷类型)", "备注": "备注1, 备注2", "摘要说明": "交易业务类型, 交易用途类型", "IP": "", "MAC": "", "原始信息": "用户侧网银联单号, 网联/银联, 第三方账户名称, 对手侧网银联单号, 网联/银联.1, 第三方账户名称.1, 对手方接收时间, 对手方接收金额(元)"}, "transform": {"normalize_income_expense": "将 '入' 转换为 '进', '出' 转换为 '出'"}}