{"source": "支付宝", "mapping": {"交易类型": "支付宝", "交易方姓名": "extract_name(用户信息)", "交易方账户": "extract_account(用户信息)", "交易方开户银行": "支付宝", "交易方身份证": "", "对手方姓名": "extract_name(消费名称)", "对手方账户": "extract_account(消费名称)", "对手方开户银行": "", "对手方身份证": "", "交易流水号": "交易号", "交易时间": "latest(交易创建时间, 付款时间, 最近修改时间)", "交易金额": "金额（元）", "交易余额": "", "收付标志": "normalize_income_expense(收/支)", "备注": "备注", "摘要说明": "类型", "IP": "", "MAC": "", "原始信息": "对应的协查数据"}, "transform": {"extract_name": "从字段中提取姓名，例如 '****************(陈家慧)' -> '陈家慧'", "extract_account": "从字段中提取账户，例如 '****************(陈家慧)' -> '****************'", "latest": "从多个时间字段中选择最新的时间", "normalize_income_expense": "将 '收入' 转换为 '进', '支出' 转换为 '出'"}}