app:
  upload-dir: files
  classified-dir: classified
  output-dir: output
spring:
  servlet:
    multipart:
      max-file-size: 2000MB
      max-request-size: 2000MB
      resolve-lazily: true
  datasource:
    url: *****************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      pool-name: BankDataHikariPool
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 30000
      validation-timeout: 5000
      leak-detection-threshold: 60000
  jpa:
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        show_sql: false
        format_sql: true
        jdbc:
          batch_size: 1000
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
        generate_statistics: false
    open-in-view: false
server:
  port: 8080
# Spark相关配置
spark:
  app:
    name: "xiaowuDemoClean"
  memory:
    offHeap:
      enabled: "true"
      size: "2g"
  executor:
    memory: "4g"
  driver:
    memory: "4g"
  default:
    parallelism: "20"
  sql:
    shuffle:
      partitions: "20"

# 数据库性能配置
database:
  batch:
    size: 1000
    timeout: 30
  connection:
    max-retry: 3
    retry-delay: 1000
  performance:
    enable-batch-insert: true
    enable-parallel-processing: true
    max-parallel-threads: 4
