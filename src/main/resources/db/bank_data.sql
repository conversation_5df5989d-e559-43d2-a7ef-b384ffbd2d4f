CREATE TABLE bank_transactions (
                                   id BIGINT AUTO_INCREMENT PRIMARY KEY,
                                   transaction_card_no VARCHAR(50),
                                   transaction_account VARCHAR(50),
                                   account_holder_name VARCHAR(100),
                                   account_holder_id VARCHAR(50),
                                   transaction_time TIMESTAMP,
                                   transaction_amount DECIMAL(15,2),
                                   transaction_balance DECIMAL(15,2),
                                   income_expense_flag VARCHAR(10),
                                   counterpart_account VARCHAR(50),
                                   cash_flag VARCHAR(10),
                                   counterpart_name VARCHAR(100),
                                   summary_description TEXT,
                                   created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                   INDEX idx_card_no (transaction_card_no),
                                   INDEX idx_account (transaction_account),
                                   INDEX idx_time (transaction_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
