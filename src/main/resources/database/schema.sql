-- 银行数据清洗工具数据库表结构
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS bank_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE bank_data;

-- 1. 交易流水表（使用中文字段名）
-- auto-generated definition
create table 交易明细
(
    交易卡号         text not null,
    交易账号         text not null,
    交易方户名       text not null,
    交易方证件号码   text not null,
    交易时间         text not null,
    交易金额         text not null,
    交易余额         text not null,
    收付标志         text not null,
    交易对手账卡号   text not null,
    现金标志         text not null,
    对手户名         text not null,
    对手身份证号     text not null,
    对手开户银行     text not null,
    摘要说明         text not null,
    交易币种         text not null,
    交易网点名称     text not null,
    交易发生地       text not null,
    交易是否成功     text not null,
    传票号           text not null,
    IP地址           text not null,
    MAC地址          text not null,
    对手交易余额     text not null,
    交易流水号       text not null,
    日志号           text not null,
    凭证种类         text not null,
    凭证号           text not null,
    交易柜员号       text not null,
    备注             text not null,
    商户名称         text not null,
    交易类型         text not null,
    查询反馈结果原因 text not null,
    开户行           text not null,
    文件路径         text not null
)
    collate = utf8mb4_general_ci;


-- 2. 人员信息表（使用中文字段名）
-- auto-generated definition
create table 人员信息
(
    客户名称         text not null,
    证照类型         text not null,
    证照号码         text not null,
    单位地址         text not null,
    联系电话         text not null,
    联系手机         text not null,
    单位电话         text not null,
    住宅电话         text not null,
    工作单位         text not null,
    邮箱地址         text not null,
    代办人姓名       text not null,
    代办人证件类型   text not null,
    代办人证件号码   text not null,
    国税纳税号       text not null,
    地税纳税号       text not null,
    法人代表         text not null,
    法人代表证件类型 text not null,
    法人代表证件号码 text not null,
    出生日期         text not null,
    户籍地址         text not null,
    客户工商执照号码 text not null,
    文件名           text not null,
    文件路径         text not null
)
    collate = utf8mb4_general_ci;


-- 3. 任务信息表（使用中文字段名）
CREATE TABLE `任务信息` (
                                          `任务流水号` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `银行名称` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `主体类别` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `证帐号码` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `发送时间` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `反馈时间` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `反馈结果` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `反馈非明细结果` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `反馈明细结果` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `入库时间` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `入库状态` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `请求单号` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                          `查询结果` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
-- 4. 账户信息表（使用中文字段名）
-- auto-generated definition
create table 卡号信息
(
    账户开户名称     text not null,
    开户人证件号码   text not null,
    交易卡号         text not null,
    交易账号         text not null,
    账号开户时间     text not null,
    账户余额         text not null,
    可用余额         text not null,
    币种             text not null,
    开户网点代码     text not null,
    开户网点         text not null,
    账户状态         text not null,
    钞汇标志名称     text not null,
    开户人证件类型   text not null,
    销户日期         text not null,
    账户类型         text not null,
    开户联系方式     text not null,
    通信地址         text not null,
    联系电话         text not null,
    代理人           text not null,
    代理人电话       text not null,
    备注             text not null,
    开户省份         text not null,
    开户城市         text not null,
    账号开户银行     text not null,
    客户代码         text not null,
    法人代表         text not null,
    客户工商执照号码 text not null,
    法人代表证件号码 text not null,
    住宅地址         text not null,
    邮政编码         text not null,
    代办人证件号码   text not null,
    邮箱地址         text not null,
    关联资金账户     text not null,
    地税纳税号       text not null,
    单位电话         text not null,
    代办人证件类型   text not null,
    住宅电话         text not null,
    法人代表证件类型 text not null,
    国税纳税号       text not null,
    单位地址         text not null,
    工作单位         text not null,
    销户网点         text not null,
    最后交易时间     text not null,
    账户销户银行     text not null,
    任务流水号       text not null,
    文件名           text not null,
    文件路径         text not null,
    生活轨迹         text not null,
    夜间交易         text not null,
    小额测试         text not null,
    外汇             text not null,
    虚拟币           text not null,
    存现             text not null,
    取现             text not null,
    快进快出         text not null
)
    collate = utf8mb4_general_ci;

