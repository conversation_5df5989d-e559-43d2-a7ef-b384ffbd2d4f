# JUniversalEncodingDetector 使用文档

## 1. 简介

`JUniversalEncodingDetector` 是一个基于 Mozilla 的 UniversalChardet 库的编码检测工具，专为银行清洗转换工具项目设计。它能够高效、准确地检测文件编码，特别优化了对中文编码（如 UTF-8、GBK、GB18030 等）的支持。

## 2. 主要特性

- **高效的编码检测**：使用 JUniversalChardet 库进行快速编码检测
- **智能缓存机制**：缓存已检测文件的编码，提高性能
- **多重检测策略**：结合 BOM 检测、内容分析和备选编码尝试
- **特殊情况处理**：针对特定来源（如建设银行）的文件提供专门处理
- **乱码智能识别**：增强的乱码检测算法，特别优化了中文乱码识别
- **异常处理机制**：健壮的异常处理，确保在各种情况下都能返回可用编码
- **内容类型感知**：根据文件内容类型（中文、日文等）优化编码检测

## 3. 使用方法

### 3.1 基本用法

```java
// 检测文件编码
String filePath = "path/to/your/file.csv";
String encoding = JUniversalEncodingDetector.detectFileEncoding(filePath);
logger.info("检测到文件编码: {}", encoding);

// 使用检测到的编码读取文件
Dataset<Row> df = spark.read()
        .option("header", "true")
        .option("inferSchema", "true")
        .option("encoding", encoding)
        .option("charset", encoding)
        .csv(filePath);
```

### 3.2 缓存管理

```java
// 手动缓存编码
JUniversalEncodingDetector.cacheEncoding(filePath, encoding);

// 获取缓存的编码
String cachedEncoding = JUniversalEncodingDetector.getCachedEncoding(filePath);

// 清除缓存
JUniversalEncodingDetector.clearCache();
```

## 4. 工作原理

### 4.1 检测流程

1. **特殊情况检查**：首先检查是否是特殊情况（如建设银行文件）
2. **缓存查询**：检查是否有缓存的编码
3. **BOM 检测**：检查文件是否包含 BOM 标记
4. **JUniversalChardet 检测**：使用 JUniversalChardet 库检测编码
5. **编码验证**：验证检测到的编码是否有效
6. **备选方法**：如果前面的方法都失败，尝试其他编码

### 4.2 乱码检测

乱码检测算法考虑了多种因素：

- 替换字符（�）的存在
- 控制字符的存在
- 特定的乱码字符组合（如"锟斤拷"）
- 中文编码错误的特征模式
- 不常见 Unicode 字符的比例

### 4.3 内容类型检测

通过分析文件内容，检测其语言类型：

- 中文内容：包含汉字字符
- 日文内容：包含日文字符
- 韩文内容：包含韩文字符
- ASCII 内容：主要是 ASCII 字符

## 5. 性能优化

- **缓冲区大小优化**：使用 8KB 缓冲区提高读取效率
- **读取限制**：最多读取 1MB 数据进行检测，避免处理过大文件
- **并发支持**：使用 ConcurrentHashMap 存储缓存，支持并发访问
- **编码映射优化**：智能映射检测到的编码到支持的编码

## 6. 异常处理

- **文件访问问题**：处理文件不存在、权限不足等情况
- **格式问题**：处理文件格式错误的情况
- **默认编码**：在任何异常情况下都返回默认编码（UTF-8）

## 7. 与项目集成

### 7.1 在 FileReaderUtil 中的使用

```java
public static Dataset<Row> readCsvFile(SparkSession sparkSession, String filePath) {
    // 使用 JUniversalEncodingDetector 检测文件编码
    String detectedEncoding = JUniversalEncodingDetector.detectFileEncoding(filePath);
    
    // 使用检测到的编码读取 CSV 文件
    Dataset<Row> df = sparkSession.read()
            .option("header", "true")
            .option("inferSchema", "true")
            .option("encoding", detectedEncoding)
            .option("charset", detectedEncoding)
            .csv(filePath);
    
    // 验证读取是否成功，必要时尝试备选编码
    // ...
}
```

### 7.2 在 AbstractDataTransformer 中的使用

```java
protected Dataset<Row> readFilesFromDirectory(String directory, String format) {
    // 对目录中的每个文件使用 JUniversalEncodingDetector 检测编码
    for (File file : files) {
        String filePath = file.getAbsolutePath();
        String detectedEncoding = JUniversalEncodingDetector.detectFileEncoding(filePath);
        
        // 使用检测到的编码读取文件
        // ...
    }
}
```

## 8. 注意事项

- 编码检测不是 100% 准确的，特别是对于小文件或内容较少的文件
- 对于特殊格式的文件（如 Excel），编码检测可能不适用
- 在处理大量文件时，建议定期清除缓存以避免内存占用过高
- 对于重要文件，建议在检测后验证内容是否正确显示 