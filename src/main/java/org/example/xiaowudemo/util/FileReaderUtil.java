package org.example.xiaowudemo.util;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;
import org.apache.spark.sql.expressions.Window;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.example.xiaowudemo.transformData.util.JUniversalEncodingDetector;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.ArrayList;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.StructType;
/**
 * 文件读取工具类，提供读取各种格式文件的方法,提供了编码检测，乱码检查的功能
 */
public class FileReaderUtil {
    private static final Logger logger = LoggerFactory.getLogger(FileReaderUtil.class);
/**
     * 根据文件格式读取目录中的文件并合并
     * @param sparkSession Spark会话
     * @param directory 目录路径
     * @param fileFormat 文件格式
     * @return 合并后的数据集
     */
    public static Dataset<Row> readDirectory(SparkSession sparkSession, String directory, String fileFormat) {
        return readDirectory(sparkSession, directory, fileFormat, false);
    }
    /**
     * 根据文件格式读取目录中的文件并合并，使用emptyDataFrame作为一个容错机制
     * @param sparkSession Spark会话
     * @param directory 目录路径
     * @param fileFormat 文件格式
     * @param throwOnError 是否在出错时抛出异常
     * @return 合并后的数据集
     */
    public static Dataset<Row> readDirectory(SparkSession sparkSession, String directory, String fileFormat, boolean throwOnError) {
        logger.info("读取目录中的{}文件: {}", fileFormat, directory);
        try {
            File dir = new File(directory);
            if (!dir.exists() || !dir.isDirectory()) {
                logger.error("目录不存在或不是目录: {}", directory);
                return sparkSession.emptyDataFrame();
            }
            Dataset<Row> result;
            switch (fileFormat.toLowerCase()) {
                case "csv":
                    result = readFilesFromDirectory(sparkSession, directory, ".csv", ",");
                    break;
                case "txt":
                    result = readFilesFromDirectory(sparkSession, directory, ".txt", "\t");
                    break;
                case "xlsx":
                case "xls":
                    result = readExcelFilesFromDirectory(sparkSession, directory);
                    break;
                default:
                    logger.error("不支持的文件格式: {}", fileFormat);
                    return sparkSession.emptyDataFrame();
            }
            
            // 确保数据集有源文件名和行号列
            if (!result.isEmpty()) {
                boolean hasSourceFile = Arrays.asList(result.columns()).contains("源文件名");
                boolean hasRowNum = Arrays.asList(result.columns()).contains("行号");
                
                if (!hasSourceFile) {
                    logger.info("数据集中缺少源文件名列，添加默认值");
                    result = result.withColumn("源文件名", functions.lit("未知文件"));
                }
                
                if (!hasRowNum) {
                    logger.info("数据集中缺少行号列，添加行号");
                    result = result.withColumn("行号", functions.row_number().over(Window.orderBy(functions.monotonically_increasing_id())));
                }
                
                // 记录读取到的行数信息
                long rowCount = result.count();
                logger.info("从目录 {} 读取到 {} 行数据", directory, rowCount);
            }
            
            return result;
        } catch (Exception e) {
            logger.error("读取目录时发生错误: {}, 错误: {}", directory, e.getMessage(), e);
            if (throwOnError) {
                throw new RuntimeException("读取目录失败: " + directory, e);
            }
            return sparkSession.emptyDataFrame();
        }
    }
    
    /**
     * 读取目录中的CSV或TXT文件并合并，CSV和TXT文件复用一个私有方法
     * @param sparkSession Spark会话
     * @param directory 目录路径
     * @param fileSuffix 文件后缀
     * @param delimiter 分隔符
     * @return 合并后的数据集
     */
    private static Dataset<Row> readFilesFromDirectory(SparkSession sparkSession, String directory, 
                                                       String fileSuffix, String delimiter) {
        File dir = new File(directory);
        File[] files = dir.listFiles((d, name) -> name.toLowerCase().endsWith(fileSuffix));
        if (files == null || files.length == 0) {
            logger.warn("目录中没有找到{}文件: {}", fileSuffix, directory);
            return sparkSession.emptyDataFrame();
        }
        Dataset<Row> mergedDf = null;//显式转换,也可以不显示转换
        for (File file : files) {
            String filePath = file.getAbsolutePath();
            logger.info("处理文件: {}", filePath);
            try {
                String bestEncoding = findBestEncoding(sparkSession, filePath, delimiter);
                logger.info("为文件 {} 选择最佳编码: {}", file.getName(), bestEncoding);
                Dataset<Row> df = readFileWithEncoding(sparkSession, filePath, bestEncoding, delimiter);
                if (df.isEmpty()) {
                    logger.warn("文件读取结果为空：{}", filePath);
                    continue;
                }
                // 添加源文件名和行号列
                df = df.withColumn("源文件名", functions.lit(file.getName()))
                        .withColumn("行号", functions.monotonically_increasing_id().plus(1))
                        // 添加文件路径信息
                        .withColumn("文件路径", functions.lit(file.getAbsolutePath()));
                        
                // 确保文件源信息被捕获并保留
                df = df.withColumn("原始数据信息位置", functions.concat(
                    functions.lit("文件:"),
                    functions.col("源文件名"),
                    functions.lit(":第"),
                    functions.col("行号").cast("string"),
                    functions.lit("行")
                ));
                // 记录使用的编码
                sparkSession.conf().set("file.input.encoding." + file.getName(), bestEncoding);
                // 合并数据集
                if (mergedDf == null) {
                    mergedDf = df;
                } else {
                    // 使用unionByName提高效率，自动处理列不匹配情况
                    mergedDf = mergedDf.unionByName(df, true);
                }
            } catch (Exception e) {
                logger.warn("处理文件 {} 时发生错误: {}", filePath, e.getMessage());
            }
        }
        
        return mergedDf != null ? mergedDf : sparkSession.emptyDataFrame();
    }
    
    /**
     * 查找最佳编码
     * @param sparkSession Spark会话
     * @param filePath 文件路径
     * @param delimiter 分隔符
     * @return 最佳编码
     */
    private static String findBestEncoding(SparkSession sparkSession, String filePath, String delimiter) {
        // 清除编码检测器的缓存，确保获取最新检测结果
        JUniversalEncodingDetector.clearCache();
        String detectedEncoding = JUniversalEncodingDetector.detectFileEncoding(filePath);
        logger.info("JUniversalEncodingDetector检测到文件 {} 的编码为: {}", filePath, detectedEncoding);
        
        // 检查文件名和路径是否包含中文字符
        String fileName = new File(filePath).getName();
        // 检查文件类型
        boolean isCSV = fileName.toLowerCase().endsWith(".csv");
        
        // 根据文件特征设置编码优先级
        String[] chinesePriority = {"GB18030", "GBK", "GB2312", "UTF-8"};
        String[] normalPriority = {"UTF-8", "GB18030", "GBK"};
        String[] priorityEncodings = isCSV ? chinesePriority : normalPriority;
        
        // 如果检测到的编码是有效的，先尝试使用它
        if (detectedEncoding != null && !detectedEncoding.isEmpty()) {
            try {
                Dataset<Row> df = readFileWithEncoding(sparkSession, filePath, detectedEncoding, delimiter);
                
                // 检查数据集是否有效，并且没有明显的乱码
                boolean datasetIsValid = df != null && !df.isEmpty() && df.columns().length > 1;
                boolean hasNoGarbledText = !checkForGarbledText(df);
                
                if (datasetIsValid && hasNoGarbledText) {
                    logger.info("成功使用检测到的编码 {} 读取文件，无乱码", detectedEncoding);
                    return detectedEncoding;
                }
            } catch (Exception e) {
                logger.warn("使用检测到的编码 {} 读取文件失败: {}", detectedEncoding, e.getMessage());
            }
        }
        
        // 尝试优先级编码列表
        for (String encoding : priorityEncodings) {
            // 跳过已尝试过的编码
            if (encoding.equalsIgnoreCase(detectedEncoding)) {
                continue;
            }
            
            try {
                logger.info("尝试使用备选编码 {} 读取文件", encoding);
                Dataset<Row> df = readFileWithEncoding(sparkSession, filePath, encoding, delimiter);
                
                // 验证数据集是否有效且无乱码
                if (df != null && !df.isEmpty() && df.columns().length > 1 && !checkForGarbledText(df)) {
                    logger.info("成功使用备选编码 {} 读取文件，无乱码", encoding);
                    return encoding;
                }
            } catch (Exception e) {
                logger.warn("使用备选编码 {} 读取文件失败: {}", encoding, e.getMessage());
            }
        }
        
        // 如果所有尝试都失败，返回初次检测的编码或默认编码
        String finalEncoding = detectedEncoding != null && !detectedEncoding.isEmpty() ? detectedEncoding : "UTF-8";
        logger.info("尝试了所有可能的编码，为文件 {} 选择最佳编码: {}", fileName, finalEncoding);
        return finalEncoding;
    }
    /**
     * 使用指定编码读取单个文件
     * @param sparkSession Spark会话
     * @param filePath 文件路径
     * @param encoding 编码
     * @param delimiter 分隔符
     * @return 数据集
     */
    private static Dataset<Row> readFileWithEncoding(SparkSession sparkSession, String filePath, 
                                                  String encoding, String delimiter) {
        logger.info("使用编码 {} 读取文件: {}", encoding, filePath);
        try {
            // 创建通用的全字符串模式 - 所有列都作为字符串处理
            // 先读取标题行，确定列数和列名
            List<String> headers = new ArrayList<>();
            Set<String> uniqueHeaders = new HashSet<>();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(
                            new FileInputStream(filePath), encoding))) {
                String headerLine = reader.readLine();
                if (headerLine != null) {
                    // 根据分隔符拆分标题行
                    String[] headerArray = headerLine.split(delimiter, -1);
                    for (String header : headerArray) {
                        // 清理标题，移除引号和空白字符
                        String cleanHeader = header.replaceAll("^\"|\"$", "").trim();
                        if(!uniqueHeaders.contains(cleanHeader))
                        {
                            uniqueHeaders.add(cleanHeader);
                            headers.add(cleanHeader);
                        }else{
                            logger.warn("发现重复列名: '{}',只保留第一次出现的列名",cleanHeader);
                        }
                    }
                    }
                }catch (Exception e) {
                logger.warn("读取文件标题失败，将使用默认列名: {}", e.getMessage());
            }
            
            // 创建全字符串模式
            StructType allStringSchema = new StructType();
            if (!headers.isEmpty()) {
                // 使用读取到的列名创建模式
                for (String header : headers) {
                    allStringSchema = allStringSchema.add(header, DataTypes.StringType);
                }
                logger.info("已创建全字符串模式，列数: {}", headers.size());
            } else {
                logger.warn("无法获取列标题，将使用自动推断");
            }
            
            // 读取文件
            Dataset<Row> df;
            if (!headers.isEmpty()) {
                // 使用自定义模式读取，完全避免任何类型推断
                df = sparkSession.read()
                        .option("header", "true")
                        .option("encoding", encoding)
                        .option("charset", encoding)
                        .option("delimiter", delimiter)
//                        .option("quote", "\"")
//                        .option("escape", "\\")
                        .option("multiLine", "true")
                        .schema(allStringSchema)  // 指定全字符串模式
                        .csv(filePath);
                logger.info("使用全字符串模式读取文件，避免科学计数法问题");
            } else {
                // 如果无法获取列名，回退到之前的逻辑但禁用Schema推断
                df = sparkSession.read()
                        .option("header", "true")
                        .option("inferSchema", "false")  // 禁用模式推断
                        .option("encoding", encoding)
                        .option("charset", encoding)
                        .option("delimiter", delimiter)
                        .option("quote", "\"")
                        .option("escape", "\\")
                        .option("multiLine", "true")
                        .csv(filePath);
                logger.info("无法获取列名，使用默认字符串模式读取");
            }
            
            if (df != null && !df.isEmpty()) {
                logger.info("成功读取文件，列数: {}", df.columns().length);
                // 记录前5行的第1列内容，用于编码验证
                if (df.columns().length > 0) {
                    try {
                        String firstColumn = df.columns()[0];
                        df.select(firstColumn).limit(5).collectAsList().forEach(row -> {
                            if (row.get(0) != null) {
                                logger.info("编码 {} 读取样本: [{}]", encoding, row.get(0));
                            }
                        });
                    } catch (Exception e) {
                        logger.warn("读取样本数据时出错: {}", e.getMessage());
                    }
                }
                
                // 二次检查：确保所有列都是字符串类型
                for (String column : df.columns()) {
                    String dataType = df.schema().apply(column).dataType().typeName();
                    if (!dataType.equals("string")) {
                        logger.warn("列 {} 不是字符串类型，当前类型: {}，进行强制转换", column, dataType);
                        df = df.withColumn(column, functions.col(column).cast("string"));
                    }
                }
            } else {
                logger.warn("使用编码 {} 读取的数据集为空", encoding);
            }
            return df;
        } catch (Exception e) {
            logger.error("使用编码 {} 读取文件失败: {}", encoding, e.getMessage());
            throw e;
        }
    }
    
    /**
     * 读取目录中的Excel文件
     * @param sparkSession Spark会话
     * @param directory 目录路径
     * @return 合并后的数据集
     */
    private static Dataset<Row> readExcelFilesFromDirectory(SparkSession sparkSession, String directory) {
        File directoryFile = new File(directory);
        
        if (!directoryFile.exists() || !directoryFile.isDirectory()) {
            logger.error("Excel目录无效: {}", directory);
            
            // 如果是单个文件，尝试直接读取
            if (directoryFile.exists() && !directoryFile.isDirectory() && 
                (directoryFile.getName().toLowerCase().endsWith(".xlsx") || 
                 directoryFile.getName().toLowerCase().endsWith(".xls"))) {
                logger.info("尝试直接读取Excel文件: {}", directoryFile.getAbsolutePath());
                return readExcelFile(sparkSession, directoryFile.getAbsolutePath());
            }
            
            return sparkSession.emptyDataFrame();
        }
        
        // 检查目录中是否存在Excel文件
        File[] files = directoryFile.listFiles((d, name) -> 
            name.toLowerCase().endsWith(".xlsx") || name.toLowerCase().endsWith(".xls"));
        
        if (files == null || files.length == 0) {
            logger.warn("目录中没有找到Excel文件: {}", directory);
            return sparkSession.emptyDataFrame();
        }
        
        try {
            // 使用glob模式一次性读取目录中的所有Excel文件
            Dataset<Row> df = sparkSession.read()
                .format("com.crealytics.spark.excel")
                .option("header", "true")
                .option("inferSchema", "true")
                .option("dataAddress", "'Sheet1'!A1")
                .option("treatEmptyValuesAsNulls", "true")
                .option("addColorColumns", "false")
                .load(directory + "/*.{xlsx,xls}");
            
            if (!df.isEmpty()) {
                // 使用input_file_name()获取源文件名
                df = df.withColumn("源文件名", functions.element_at(functions.split(functions.input_file_name(), "/"), -1))
                       .withColumn("行号", functions.row_number().over(Window.orderBy(functions.monotonically_increasing_id())));
                
                logger.info("成功批量读取Excel文件，列数: {}，行数: {}", df.columns().length, df.count());
            }
            
            return df;
        } catch (Exception e) {
            logger.error("批量读取Excel文件失败: {}, 尝试逐个读取", e.getMessage());
            
            // 如果批量读取失败，回退到逐个读取文件
            Dataset<Row> mergedDf = null;
            
            for (File file : files) {
                try {
                    String filePath = file.getAbsolutePath();
                    logger.info("单独处理Excel文件: {}", filePath);
                    
                    Dataset<Row> singleDf = readExcelFile(sparkSession, filePath);
                    
                    if (singleDf.isEmpty()) {
                        logger.warn("Excel文件为空或读取失败: {}", filePath);
                        continue;
                    }
                    
                    // 添加源文件名和行号列
                    singleDf = singleDf.withColumn("源文件名", functions.lit(file.getName()))
                               .withColumn("行号", functions.monotonically_increasing_id().plus(1))
                               // 添加文件路径信息
                               .withColumn("文件路径", functions.lit(file.getAbsolutePath()));
                                
                    // 确保文件源信息被捕获并保留
                    singleDf = singleDf.withColumn("原始数据信息位置", functions.concat(
                        functions.lit("Excel文件:"),
                        functions.col("源文件名"),
                        functions.lit(":第"),
                        functions.col("行号").cast("string"),
                        functions.lit("行")
                    ));
                    
                    // 合并数据集
                    if (mergedDf == null) {
                        mergedDf = singleDf;
                    } else {
                        mergedDf = mergedDf.unionByName(singleDf, true);
                    }
                } catch (Exception ex) {
                    logger.warn("处理单个Excel文件失败: {}, 错误: {}", file.getName(), ex.getMessage());
                }
            }
            
            return mergedDf != null ? mergedDf : sparkSession.emptyDataFrame();
        }
    }
    
    /**
     * 读取单个Excel文件
     * @param sparkSession Spark会话
     * @param filePath 文件路径
     * @return 数据集
     */
    public static Dataset<Row> readExcelFile(SparkSession sparkSession, String filePath) {
        try {
            logger.info("读取Excel文件: {}", filePath);
            
            // 先使用最小配置读取头部，获取列名
            Dataset<Row> headerDf = sparkSession.read()
                    .format("com.crealytics.spark.excel")
                    .option("header", "true")
                    .option("inferSchema", "false")
                    .option("dataAddress", "'Sheet1'!A1:Z1") // 只读取第一行获取标题
                    .option("treatEmptyValuesAsNulls", "true")
                    .option("addColorColumns", "false")
                    .load(filePath);
                    
            // 创建全字符串模式
            StructType allStringSchema = new StructType();
            for (String column : headerDf.columns()) {
                allStringSchema = allStringSchema.add(column, DataTypes.StringType);
            }
            
            logger.info("已为Excel文件创建全字符串模式，列数: {}", headerDf.columns().length);
            
            // 使用全字符串模式读取完整文件
            Dataset<Row> df = sparkSession.read()
                    .format("com.crealytics.spark.excel")
                    .option("header", "true")
                    .option("inferSchema", "false")
                    .option("dataAddress", "'Sheet1'!A1")
                    .option("treatEmptyValuesAsNulls", "true")
                    .option("addColorColumns", "false")
                    .schema(allStringSchema)
                    .load(filePath);
            
            if (!df.isEmpty()) {
                logger.info("成功读取Excel文件: {}, 列数: {}", filePath, df.columns().length);
                
                // 二次检查：确保所有列都是字符串类型
                for (String column : df.columns()) {
                    String dataType = df.schema().apply(column).dataType().typeName();
                    if (!dataType.equals("string")) {
                        logger.warn("Excel列 {} 不是字符串类型，当前类型: {}，进行强制转换", column, dataType);
                        df = df.withColumn(column, functions.col(column).cast("string"));
                    }
                }
            }
            return df;
        } catch (Exception e) {
            logger.error("读取Excel文件失败: {}, 错误: {}", filePath, e.getMessage());
            // 如果创建模式方法失败，尝试使用简单方法但强制所有列为字符串
            try {
                logger.info("尝试使用备用方法读取Excel文件: {}", filePath);
                Dataset<Row> df = sparkSession.read()
                        .format("com.crealytics.spark.excel")
                        .option("header", "true")
                        .option("inferSchema", "false")
                        .option("dataAddress", "'Sheet1'!A1")
                        .option("treatEmptyValuesAsNulls", "true")
                        .option("addColorColumns", "false")
                        .load(filePath);
                
                // 将所有列转换为字符串类型
                for (String column : df.columns()) {
                    df = df.withColumn(column, functions.col(column).cast("string"));
                }
                
                return df;
            } catch (Exception ex) {
                logger.error("备用方法读取Excel文件也失败: {}, 错误: {}", filePath, ex.getMessage());
                return sparkSession.emptyDataFrame();
            }
        }
    }
    
    /**
     * 根据文件格式读取单个文件
     * @param sparkSession Spark会话
 * @param filePath 文件路径
 * @param fileFormat 文件格式
     * @return 数据集
 */
    public static Dataset<Row> readFile(SparkSession sparkSession, String filePath, String fileFormat) {
        return switch (fileFormat.toLowerCase()) {
            case "csv" -> readCsvFile(sparkSession, filePath);
            case "xlsx", "xls" -> readExcelFile(sparkSession, filePath);
            case "txt" -> readTxtFile(sparkSession, filePath);
            default -> throw new IllegalArgumentException("不支持的文件格式:" + fileFormat);
        };
    }

    /**
     * 读取CSV文件
     * @param sparkSession Spark会话
     * @param filePath 文件路径
     * @return 数据集
     */
    public static Dataset<Row> readCsvFile(SparkSession sparkSession, String filePath) {
        return readCsvFile(sparkSession, filePath, ",");
    }
    
    /**
     * 读取CSV文件，指定分隔符
     * @param sparkSession Spark会话
     * @param filePath 文件路径
     * @param delimiter 分隔符
     * @return 数据集
     */
    public static Dataset<Row> readCsvFile(SparkSession sparkSession, String filePath, String delimiter) {
        logger.info("读取CSV文件: {}, 分隔符: '{}'", filePath, delimiter);
        
        // 使用统一的编码检测逻辑
        String bestEncoding = findBestEncoding(sparkSession, filePath, delimiter);

        return readFileWithEncoding(sparkSession, filePath, bestEncoding, delimiter);
    }
    
    /**
     * 读取TXT文件，使用默认分隔符(\t)
     * @param sparkSession Spark会话
     * @param filePath 文件路径
     * @return 数据集
     */
    public static Dataset<Row> readTxtFile(SparkSession sparkSession, String filePath) {
        return readTxtFile(sparkSession, filePath, "\t");
    }
    
    /**
     * 读取TXT文件，指定分隔符
     * @param sparkSession Spark会话
     * @param filePath 文件路径
     * @param delimiter 分隔符
     * @return 数据集
     */
    public static Dataset<Row> readTxtFile(SparkSession sparkSession, String filePath, String delimiter) {
        logger.info("读取TXT文件: {}, 分隔符: '{}'", filePath, delimiter);
        
        // 使用统一的编码检测逻辑
        String bestEncoding = findBestEncoding(sparkSession, filePath, delimiter);
        
        // 如果默认分隔符没有成功读取，尝试其他常见分隔符
        Dataset<Row> df = readFileWithEncoding(sparkSession, filePath, bestEncoding, delimiter);
        
        if (df.isEmpty() || df.columns().length <= 1) {
            // 只有在默认分隔符失败时才尝试其他分隔符
            logger.info("使用默认分隔符 '{}' 读取失败，尝试其他分隔符", delimiter);
            String[] alternativeDelimiters = {",", "|", ";"};
            
            for (String altDelimiter : alternativeDelimiters) {
                if (altDelimiter.equals(delimiter)) continue;
                
                try {
                    Dataset<Row> tempDf = readFileWithEncoding(sparkSession, filePath, bestEncoding, altDelimiter);
                    if (!tempDf.isEmpty() && tempDf.columns().length > 1) {
                        logger.info("成功使用分隔符 '{}' 读取文件", altDelimiter);
                            return tempDf;
                        }
                    } catch (Exception e) {
                    // 继续尝试下一个分隔符
                }
            }
        }
        
        return df;
    }
    
    /**
     * 检查数据集是否包含乱码
     * @param df 要检查的数据集
     * @return 是否包含乱码
     */
    private static boolean checkForGarbledText(Dataset<Row> df) {
        if (df == null || df.isEmpty()) {
            return false;
        }
        
        // 检查列名 - 列名乱码是一个明确的信号
        int garbledColumnCount = 0;
        int totalColumns = df.columns().length;
        
        for (String colName : df.columns()) {
            // 清理列名
            String cleanColumnName = cleanText(colName);
            
            // 检查清理后的列名是否包含乱码
            if (!cleanColumnName.isEmpty() && containsGarbledText(cleanColumnName)) {
                logger.warn("列名包含乱码: '{}'", colName);
                garbledColumnCount++;
            }
        }
        
        // 如果超过30%的列名包含乱码，则认为存在乱码问题
        if (garbledColumnCount > 0 && (double)garbledColumnCount / totalColumns > 0.3) {
            logger.warn("检测到列名乱码比例较高: {}%", String.format("%.1f", (double)garbledColumnCount / totalColumns * 100));
            return true;
        }
        
        try {
            // 检查样本数据 - 取少量样本进行检查，避免过多IO
            Dataset<Row> sampleRows = df.limit(3);
            List<Row> samples = sampleRows.collectAsList();
            
            int totalFields = 0;
            int garbledFields = 0;
            
            for (Row row : samples) {
                for (int i = 0; i < row.length(); i++) {
                    if (row.get(i) != null) {
                        String value = row.get(i).toString();
                        totalFields++;
                        
                        // 清理值
                        String cleanValue = cleanText(value);
                        
                        if (!cleanValue.isEmpty() && containsGarbledText(cleanValue)) {
                            logger.debug("数据字段包含乱码: '{}'", value);
                            garbledFields++;
                        }
                    }
                }
            }
            // 只有当明确超过阈值时才判定为乱码
            if (totalFields > 0 && (double)garbledFields / totalFields > 0.3) {
                logger.warn("检测到数据样本乱码比例较高: {}%", String.format("%.1f", (double)garbledFields / totalFields * 100));
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.warn("检查数据样本时出错: {}", e.getMessage());
            return false;  // 出错时保守处理，不将其视为乱码
        }
    }
    
    /**
     * 清理文本，移除制表符、换行符等特殊字符
     * @param text 需要清理的文本
     * @return 清理后的文本
     */
    private static String cleanText(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }
        
        // 移除制表符、换行符、回车符并修剪空白
        return text.replace("\t", "").replace("\n", "").replace("\r", "").trim();
    }
    
    /**
     * 检查文本是否包含乱码
     * @param text 要检查的文本
     * @return 是否包含乱码
     */
    public static boolean containsGarbledText(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        // 清理文本，使用独立的清理方法
        String cleanText = cleanText(text);
        
        // 如果清理后文本为空，则不是乱码
        if (cleanText.isEmpty()) {
            return false;
        }
        
        // 忽略科学计数法，如 2.0220508220014836E27
        if (cleanText.matches("-?\\d+(\\.\\d+)?([Ee][+-]?\\d+)?")) {
            return false;
        }
        
        // 检查是否为常见的汉字、英文字母、数字和常见标点符号
        String validPunctuation = ".:,;?!()[]{}+-*/=_%$#@&|~'\"";  // 英文标点
        String chinesePunctuation = "\u3001\u3002\uff0c\uff0e\uff1a\uff1b\uff1f\uff01\u201c\u201d\u2018\u2019\u300a\u300b\u3008\u3009\u3010\u3011"; // 中文标点
        
        // 使用正则表达式匹配合法字符
        boolean mostlyValid = cleanText.chars().mapToObj(c -> (char) c)
            .allMatch(c -> 
                (c >= 'a' && c <= 'z') || // 小写字母
                (c >= 'A' && c <= 'Z') || // 大写字母
                (c >= '0' && c <= '9') || // 数字
                (c >= '\u4e00' && c <= '\u9fa5') || // 常见汉字范围
                validPunctuation.indexOf(c) >= 0 || // 英文标点符号
                chinesePunctuation.indexOf(c) >= 0   // 中文标点符号
            );
        
        // 如果大部分字符都是有效的，则认为不是乱码
        if (mostlyValid) {
            return false;
        }
        return true;
    }
}

