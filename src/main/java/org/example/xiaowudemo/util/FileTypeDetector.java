package org.example.xiaowudemo.util;

import org.example.xiaowudemo.configure.ConfigManager;
import org.example.xiaowudemo.transformData.util.JUniversalEncodingDetector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.apache.poi.ss.usermodel.*;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class FileTypeDetector {
    private static final Logger logger = LoggerFactory.getLogger(FileTypeDetector.class);
    private final ConfigManager configManager;
    private final BankFileSubTypeDetector bankFileSubTypeDetector;
    @Autowired
    public FileTypeDetector(ConfigManager configManager, BankFileSubTypeDetector bankFileSubTypeDetector) {
        this.configManager = configManager;
        this.bankFileSubTypeDetector = bankFileSubTypeDetector;
    }
    /**
    * 主处理程序，负责根据文件后缀分类
    */
    public String detectFileType(File file){
        String fileName  = file.getName().toLowerCase();
        try{
            if(fileName.endsWith(".csv") || fileName.endsWith(".txt"))
            {
                return detectTextFileType(file);
            }else if (fileName.endsWith("xlsx"))
            {
                return detectExcelFileType(file);
            }
            else {
                logger.info("暂时不支持文件类型:{},请检查文件类型是否为csv，txt，xlsx",fileName);
                return "未知";
            }
        }catch (Exception e){
            logger.error("检测文件类型失败:{},错误:{},",fileName,e.getMessage());
            return "未知";
        }
    }

    // 检查文本是否包含乱码的辅助方法
    private boolean containsGarbledText(String text) {
        // 检查常见的乱码特征，返回true表示有乱码
        return text.contains("\uFFFD") || // Unicode替换字符，表示未知字符
                text.contains("锟") || // 常见中文乱码字符
                text.contains("烫") || // 常见中文乱码字符
                text.contains("锔") || // 常见中文乱码字符
                text.contains("?") || // 问号，常用于替换无法识别的字符
                // 检查是否包含不可打印的控制字符（除了制表符、换行符、回车符外）
                text.chars().anyMatch(c -> (c < 32 && c != 9 && c != 10 && c != 13) || c == 65533);
    }

    /**
     * 根据表头分析属于什么文件类型
     * @param headers 传入的表头参数
     * @return 得分最高的文件类型参数
     */
    private String categorizeByHeaders(String[] headers){
        Map<String,Integer> scores = new HashMap<>();
        for(String fileType : configManager.getAllFileTypes())
        {
            scores.put(fileType,0);
        }
        for(String header : headers)
        {
            String normalizedHeader = header.trim();
            for(String fileType : configManager.getAllFileTypes()){
                List<String> keyWords = configManager.getKeyWordsForFileType(fileType);
                if(keyWords.contains(normalizedHeader))
                {
                    scores.put(fileType,scores.get(fileType) + 2);
                    continue;
                }
                for(String keyWord : keyWords)
                {
                    if(normalizedHeader.contains(keyWord) || keyWord.contains(normalizedHeader)){
                        scores.put(fileType,scores.get(fileType) + 1);
                        break;
                    }
                }
            }
        }
        String bestMatch = "未知";
        int maxScore = 0 ;
        for(Map.Entry<String,Integer> entry : scores.entrySet())
        {
            logger.info("文件类型{} 得分:{}",entry.getKey(),entry.getValue());
            if(entry.getValue() > maxScore)
            {
                maxScore = entry.getValue();
                bestMatch = entry.getKey();
            }
        }
        return maxScore > 0? bestMatch:"未知";
    }

    /**
     * 检测文本文件的工具（处理CSV和TXT文件）
     * @param file 传入的文本文件参数
     * @return 返回文件的类型
     */
    private String detectTextFileType(File file) {
        String fileName = file.getName().toLowerCase();
        String filePath = file.getAbsolutePath();
        boolean isCsv = fileName.endsWith(".csv");
        // 根据文件类型选择默认分隔符
        String defaultSeparator = isCsv ? "," : "\t";
        // 使用JUniversalEncodingDetector检测文件编码
        String detectedEncoding = JUniversalEncodingDetector.detectFileEncoding(filePath);
        logger.info("JUniversalEncodingDetector检测到{}文件 {} 的编码: {}", 
                   isCsv ? "CSV" : "TXT", file.getName(), detectedEncoding);
        try(BufferedReader reader = new BufferedReader(
                new InputStreamReader(
                        new FileInputStream(file), detectedEncoding
                )
        )) {
            String headerLine = reader.readLine();
            if(headerLine == null) {
                logger.info("表头为空，可能是空文件");
                return "未知";
            }
            String[] headers = headerLine.split(defaultSeparator);
            if (!isCsv && headers.length <= 1) {
                headers = headerLine.split("\\s+");
            }
            boolean hasGarbledText = false;
            for(int i = 0; i < headers.length; i++) {
                headers[i] = headers[i].replaceAll("\"", "").trim();
                if(containsGarbledText(headers[i])) {
                    hasGarbledText = true;
                    logger.warn("使用{}编码读取表头时发现乱码: '{}'", detectedEncoding, headers[i]);
                }
                logger.info("使用{}编码读取{}表头: '{}'", detectedEncoding, 
                           isCsv ? "CSV" : "TXT", headers[i]);
            }
            if (!hasGarbledText) {
                logger.info("成功使用{}编码读取文件", detectedEncoding);
                String fileType = categorizeByHeaders(headers);
                // 如果是银行文件，进行二级分类
                if ("银行".equals(fileType)) {
                    return isCsv ? 
                           bankFileSubTypeDetector.detectCsvBankSubType(file, detectedEncoding) :
                           bankFileSubTypeDetector.detectTxtBankSubType(file, detectedEncoding);
                }
                return fileType;
            }
            logger.warn("使用{}编码读取时发现乱码，尝试使用备选编码", detectedEncoding);
            JUniversalEncodingDetector.clearCache();
        } catch (Exception e) {
            logger.warn("使用{}编码读取文件失败: {}", detectedEncoding, e.getMessage());
        }
        // 尝试备选编码
        String[] fallbackEncodings = {"UTF-8", "GBK", "GB18030", "GB2312", "ISO-8859-1"};
        for (String encoding : fallbackEncodings) {
            if (encoding.equals(detectedEncoding)) {
                continue;
            }
            try (BufferedReader fallbackReader = new BufferedReader(
                    new InputStreamReader(
                            new FileInputStream(file),
                            encoding)
            )) {
                String headerLine = fallbackReader.readLine();
                if (headerLine == null) {
                    continue;
                }
                String[] headers = headerLine.split(defaultSeparator);
                if (!isCsv && headers.length <= 1) {
                    headers = headerLine.split("\\s+");
                }
                boolean hasFallbackGarbledText = false;
                for (int i = 0; i < headers.length; i++) {
                    headers[i] = headers[i].replaceAll("\"", "").trim();
                    if (containsGarbledText(headers[i])) {
                        hasFallbackGarbledText = true;
                        logger.warn("使用{}编码读取时发现乱码: '{}'", encoding, headers[i]);
                        break;
                    }
                    logger.info("使用{}编码读取{}表头: '{}'", encoding, 
                               isCsv ? "CSV" : "TXT", headers[i]);
                }
                if (!hasFallbackGarbledText) {
                    logger.info("成功使用{}编码读取文件", encoding);
                    JUniversalEncodingDetector.cacheEncoding(filePath, encoding);
                    String fileType = categorizeByHeaders(headers);
                    // 如果是银行文件，进行二级分类
                    if ("银行".equals(fileType)) {
                        return isCsv ? 
                               bankFileSubTypeDetector.detectCsvBankSubType(file, encoding) :
                               bankFileSubTypeDetector.detectTxtBankSubType(file, encoding);
                    }
                    return fileType;
                }
            } catch (Exception ex) {
                logger.warn("使用{}编码读取文件失败: {}", encoding, ex.getMessage());
            }
        }
        
        logger.warn("所有编码尝试都失败，无法读取文件");
        return "未知";
    }

    /**
     * 检测Excel文件的工具
     * @param file  传入的XlSX文件类型
     * @return  返回文件的类型
     */
    private String detectExcelFileType(File file) {
        try(FileInputStream fis = new FileInputStream(file)){
            Workbook workbook = WorkbookFactory.create(fis);
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            if(headerRow == null)
            {
                logger.info("Excel文件表头为空");
                return "未知";
            }
            List<String> headers = new ArrayList<>();
            for(int i = 0 ; i < headerRow.getLastCellNum();i++)
            {
                Cell cell = headerRow.getCell(i);
                if(cell!=null){
                    String cellValue = switch (cell.getCellType())
                    {
                        case STRING ->
                                cell.getStringCellValue();
                        case NUMERIC ->
                                String.valueOf(cell.getNumericCellValue());
                        case BOOLEAN ->
                            String.valueOf(cell.getBooleanCellValue());
                        default ->
                            "";
                    };
                    if(!cellValue.trim().isEmpty())
                    {
                        headers.add(cellValue.trim());
                        logger.info("Excel表头:{}已成功添加到列表",cellValue.trim());
                    }
                }
            }
            String fileType = categorizeByHeaders(headers.toArray(new String[0]));
            // 如果是银行文件，进行二级分类
            if ("银行".equals(fileType)) {
                return bankFileSubTypeDetector.detectExcelBankSubType(file);
            }
            return fileType;
        } catch (Exception e) {
            logger.error("读取Excel文件失败:{}", e.getMessage());
            return "未知";
        }
    }
}