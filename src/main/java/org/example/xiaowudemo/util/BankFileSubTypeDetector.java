package org.example.xiaowudemo.util;

import org.example.xiaowudemo.configure.ConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.*;
import org.apache.poi.ss.usermodel.*;

/**
 * 银行文件子类型检测器
 * 负责识别银行文件的具体子类型（交易流水、账户信息等）
 */
@Component
public class BankFileSubTypeDetector {
    private static final Logger logger = LoggerFactory.getLogger(BankFileSubTypeDetector.class);
    private final ConfigManager configManager;

    @Autowired
    public BankFileSubTypeDetector(ConfigManager configManager) {
        this.configManager = configManager;
    }
    /**
     * 检测银行文件的子类型
     * @param file 文件对象
     * @param headers 文件表头数组
     * @return 银行文件子类型（银行_交易流水、银行_账户信息等）
     */
    public String detectSubType(File file, String[] headers) {
        if (headers == null || headers.length == 0) {
            logger.warn("文件 {} 表头为空，无法进行银行子类型识别", file.getName());
            return "银行";
        }

        List<Map<String, List<String>>> subTypes = configManager.getBankSubTypes();
        if (subTypes.isEmpty()) {
            logger.warn("未配置银行子类型信息，返回默认类型：银行");
            return "银行";
        }
        Map<String, Integer> subTypeScores = new HashMap<>();
        // 初始化所有子类型得分
        for (Map<String, List<String>> subType : subTypes) {
            String subTypeName = subType.get("name").get(0);
            subTypeScores.put(subTypeName, 0);
            logger.debug("初始化银行子类型 {} 得分为 0", subTypeName);
        }
        // 计算每个子类型的匹配分数
        for (String header : headers) {
            String normalizedHeader = header.trim();
            for (Map<String, List<String>> subType : subTypes) {
                String subTypeName = subType.get("name").get(0);
                List<String> keyWords = subType.get("keyWords");
                // 完全匹配给2分
                if (keyWords.contains(normalizedHeader)) {
                    subTypeScores.put(subTypeName, subTypeScores.get(subTypeName) + 2);
                    logger.debug("表头 '{}' 完全匹配银行子类型 {} 的关键词", normalizedHeader, subTypeName);
                    continue;
                }
                // 部分匹配给1分
                for (String keyword : keyWords) {
                    if (normalizedHeader.contains(keyword) || keyword.contains(normalizedHeader)) {
                        subTypeScores.put(subTypeName, subTypeScores.get(subTypeName) + 1);
                        logger.debug("表头 '{}' 部分匹配银行子类型 {} 的关键词 '{}'", normalizedHeader, subTypeName, keyword);
                        break;
                    }
                }
            }
        }
        // 找出得分最高的子类型
        String bestSubType = "未识别银行子类型";
        int maxScore = 0;
        for (Map.Entry<String, Integer> entry : subTypeScores.entrySet()) {
            logger.info("银行子类型 {} 得分: {}", entry.getKey(), entry.getValue());
            if (entry.getValue() > maxScore) {
                maxScore = entry.getValue();
                bestSubType = entry.getKey();
            }
        }
        // 如果得分最高的子类型得分为0，返回默认类型
        if (maxScore == 0) {
            logger.warn("文件 {} 无法识别银行子类型，返回默认类型：银行", file.getName());
            return "银行";
        }
        String result = "银行_" + bestSubType;
        logger.info("文件 {} 被成功识别为银行子类型: {}", file.getName(), result);
        return result;
    }
    
    /**
     * 检测CSV文件的银行子类型
     * @param file CSV文件
     * @param encoding 文件编码
     * @return 银行文件子类型
     */
    public String detectCsvBankSubType(File file, String encoding) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), encoding))) {
            String headerLine = reader.readLine();
            if (headerLine == null) {
                logger.warn("CSV文件 {} 表头为空", file.getName());
                return "银行";
            }
            
            String[] headers = headerLine.split(",");
            for (int i = 0; i < headers.length; i++) {
                headers[i] = headers[i].replaceAll("\"", "").trim();
            }
            
            return detectSubType(file, headers);
        } catch (IOException e) {
            logger.error("读取CSV文件 {} 失败: {}", file.getName(), e.getMessage());
            return "银行";
        }
    }
    
    /**
     * 检测Excel文件的银行子类型
     * @param file Excel文件
     * @return 银行文件子类型
     */
    public String detectExcelBankSubType(File file) {
        try (FileInputStream fis = new FileInputStream(file);
             Workbook workbook = WorkbookFactory.create(fis)) {
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            
            if (headerRow == null) {
                logger.warn("Excel文件 {} 表头为空", file.getName());
                return "银行";
            }
            
            List<String> headers = new ArrayList<>();
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    String cellValue = "";
                    switch (cell.getCellType()) {
                        case STRING:
                            cellValue = cell.getStringCellValue();
                            break;
                        case NUMERIC:
                            cellValue = String.valueOf(cell.getNumericCellValue());
                            break;
                        case BOOLEAN:
                            cellValue = String.valueOf(cell.getBooleanCellValue());
                            break;
                        default:
                            cellValue = "";
                    }
                    if (!cellValue.trim().isEmpty()) {
                        headers.add(cellValue.trim());
                    }
                }
            }
            
            return detectSubType(file, headers.toArray(new String[0]));
        } catch (Exception e) {
            logger.error("读取Excel文件 {} 失败: {}", file.getName(), e.getMessage());
            return "银行";
        }
    }
    
    /**
     * 检测TXT文件的银行子类型
     * @param file TXT文件
     * @param encoding 文件编码
     * @return 银行文件子类型
     */
    public String detectTxtBankSubType(File file, String encoding) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), encoding))) {
            String headerLine = reader.readLine();
            if (headerLine == null) {
                logger.warn("TXT文件 {} 表头为空", file.getName());
                return "银行";
            }
            
            String[] headers = headerLine.split("\t");
            if (headers.length <= 1) {
                // 尝试使用空格分隔
                headers = headerLine.split("\\s+");
            }
            
            for (int i = 0; i < headers.length; i++) {
                headers[i] = headers[i].replaceAll("\"", "").trim();
            }
            
            return detectSubType(file, headers);
        } catch (IOException e) {
            logger.error("读取TXT文件 {} 失败: {}", file.getName(), e.getMessage());
            return "银行";
        }
    }
} 