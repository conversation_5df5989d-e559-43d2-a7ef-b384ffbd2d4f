package org.example.xiaowudemo.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 任务信息实体类
 * 用于存储任务执行相关的信息数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskInfo {

    private Long id;

    private String taskSerialNumber;
    private String bankName;
    private String entityCategory;
    private String accountCertificateNumber;
    private LocalDateTime sendTime;
    private LocalDateTime feedbackTime;
    private String feedbackResult;
    private String feedbackNonDetailResult;
    private String feedbackDetailResult;
    private LocalDateTime storageTime;
    private String storageStatus;
    private String requestNumber;
    private String queryResult;
    private String fileName;
    private String filePath;
}