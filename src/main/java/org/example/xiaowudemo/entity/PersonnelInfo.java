package org.example.xiaowudemo.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 人员信息实体类
 * 用于存储银行客户的个人信息数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonnelInfo {

    private Long id;

    private String customerName;
    private String certificateType;
    private String certificateNumber;
    private String companyAddress;
    private String contactPhone;
    private String mobilePhone;
    private String companyPhone;
    private String homePhone;
    private String workUnit;
    private String emailAddress;
    private String agentName;
    private String agentCertificateType;
    private String agentCertificateNumber;
    private String nationalTaxNumber;
    private String localTaxNumber;
    private String legalRepresentative;
    private String legalRepCertificateType;
    private String legalRepCertificateNumber;
    private LocalDate birthDate;
    private String registeredAddress;
    private String businessLicenseNumber;
    private String fileName;
    private String filePath;
}