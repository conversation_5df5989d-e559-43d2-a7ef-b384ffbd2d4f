package org.example.xiaowudemo.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 交易流水信息实体类
 * 用于存储银行、支付宝、微信等各种来源的交易数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BankTransaction {

    private Long id;

    private String transactionCardNumber;
    private String transactionAccount;
    private String transactionHolderName;
    private String transactionHolderId;
    private String transactionTime;
    private String transactionAmount;
    private String transactionBalance;
    private String paymentFlag;
    private String counterpartAccount;
    private String cashFlag;
    private String counterpartName;
    private String counterpartId;
    private String counterpartBank;
    private String summaryDescription;
    private String transactionCurrency;
    private String transactionBranch;
    private String transactionLocation;
    private String transactionSuccess;
    private String voucherNumber;
    private String ipAddress;
    private String macAddress;
    private String counterpartBalance;
    private String transactionSerialNumber;
    private String logNumber;
    private String voucherType;
    private String voucherId;
    private String tellerNumber;
    private String remarks;
    private String merchantName;
    private String transactionType;
    private String queryFeedbackResult;
    private String openingBank;
    private String filePath;
    private String fileName;
}