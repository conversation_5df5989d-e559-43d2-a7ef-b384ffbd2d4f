package org.example.xiaowudemo.entity;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 账户信息实体类
 * 用于存储银行账户的详细信息数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountInfo {

    private Long id;

    private String accountHolderName;

    private String holderCertificateNumber;

    private String transactionCardNumber;

    private String transactionAccount;

    private LocalDateTime accountOpeningTime;

    private BigDecimal accountBalance;

    private BigDecimal availableBalance;

    private String currency;

    private String openingBranchCode;

    private String openingBranch;

    private String accountStatus;

    private String cashExchangeFlag;

    private String holderCertificateType;

    private LocalDate accountClosingDate;

    private String accountType;

    private String openingContact;

    private String communicationAddress;

    private String contactPhone;

    private String agent;

    private String agentPhone;

    private String remarks;

    private String openingProvince;

    private String openingCity;

    private String accountOpeningBank;

    private String customerCode;

    private String legalRepresentative;

    private String businessLicenseNumber;

    private String legalRepCertificateNumber;

    private String residentialAddress;

    private String postalCode;

    private String agentCertificateNumber;

    private String emailAddress;

    private String relatedFundAccount;

    private String localTaxNumber;

    private String companyPhone;

    private String agentCertificateType;

    private String homePhone;

    private String legalRepCertificateType;

    private String nationalTaxNumber;

    private String companyAddress;

    private String workUnit;

    private String closingBranch;

    private LocalDateTime lastTransactionTime;

    private String accountClosingBank;

    private String taskSerialNumber;

    private String fileName;

    private String filePath;
}