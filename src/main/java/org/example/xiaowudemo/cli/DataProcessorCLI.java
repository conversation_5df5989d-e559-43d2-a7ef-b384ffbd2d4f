package org.example.xiaowudemo.cli;

import org.apache.spark.sql.SparkSession;
import org.example.xiaowudemo.service.DatabaseService;
import org.example.xiaowudemo.service.FileService;
import org.example.xiaowudemo.service.SparkService;
import org.example.xiaowudemo.transformData.UDFRegistryFunction.UDFRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 数据处理命令行工具
 * 用于一键处理指定目录下的文件
 */
@Component
public class DataProcessorCLI implements CommandLineRunner {
    private static final Logger logger = LoggerFactory.getLogger(DataProcessorCLI.class);
    
    private final FileService fileService;
    
    private final SparkService sparkService;
    
    private final DatabaseService databaseService;

    public DataProcessorCLI(FileService fileService, SparkService sparkService, DatabaseService databaseService) {
        this.fileService = fileService;
        this.sparkService = sparkService;
        this.databaseService = databaseService;
    }

    @Override
    public void run(String... args) {
        logger.info("CommandLineRunner开始执行");
        if (args.length < 1) {
            logger.warn("未提供输入目录参数");
            System.out.println("用法: java -jar xiaowudemo.jar <输入目录>");
            System.out.println("示例: java -jar xiaowudemo.jar ./input");
            return;
        }

        String inputDir = args[0];
        logger.info("从命令行接收的输入目录参数: {}", inputDir);
        System.out.println("开始处理输入目录: " + inputDir);

        try {
            // 使用SparkService获取已创建的SparkSession
            logger.info("获取已创建的SparkSession");
            SparkSession spark = sparkService.getSparkSession();
            
            // 注册UDF函数
            logger.info("注册UDF函数");
            UDFRegistry.register(spark);
            
            // 调用FileService处理输入目录
            logger.info("调用FileService处理输入目录: {}", inputDir);
            fileService.processInputDirectory(inputDir);
            logger.info("文件处理完成");
            
            // 执行最终的联表更新操作
            performFinalDatabaseUpdate();
            
            // 设置标志并直接退出应用程序
            System.setProperty("app.processing.complete", "true");
            logger.info("所有处理已完成，即将退出应用程序");
            
            // 主动调用SparkService的cleanup确保资源释放
            if (sparkService instanceof org.example.xiaowudemo.service.impl.SparkServiceImpl) {
                logger.info("主动清理Spark资源");
                ((org.example.xiaowudemo.service.impl.SparkServiceImpl) sparkService).cleanup();
            }
            
            // 等待1秒后退出应用程序
            Thread.sleep(1000);
            logger.info("应用程序即将退出");
            System.exit(0);
        } catch (Exception e) {
            logger.error("处理失败: {}", e.getMessage(), e);
            System.out.println("处理失败: " + e.getMessage());
            // 即使处理失败，也设置标志以确保应用能退出
            System.setProperty("app.processing.complete", "true");
            logger.info("处理失败，已设置处理完成标志，应用将退出");
            System.exit(1);
        }
    }
    
    /**
     * 执行最终的数据库更新操作
     * 包括联表更新和统计信息输出
     */
    private void performFinalDatabaseUpdate() {
        if (databaseService == null) {
            logger.warn("数据库服务未配置，跳过最终数据库更新");
            return;
        }
        
        try {
            logger.info("=== 开始执行最终数据库更新操作 ===");
            
            // 1. 检查数据库连接
            if (!databaseService.checkDatabaseConnection()) {
                logger.error("数据库连接失败，跳过最终更新操作");
                return;
            }
            
            // 2. 执行最终的联表更新
            logger.info("执行最终联表更新...");
            int finalUpdatedRecords = databaseService.performJoinTableUpdate();
            logger.info("最终联表更新完成: {} 条记录的证件号码得到补充", finalUpdatedRecords);
            
            // 3. 输出最终的数据库统计信息
            logger.info("=== 最终数据库统计信息 ===");
            String finalStats = databaseService.getDatabaseStats();
            logger.info("\n{}", finalStats);
            
            // 4. 输出到控制台（用户可见）
            System.out.println("\n=== 数据库处理完成 ===");
            System.out.println("最终联表更新: " + finalUpdatedRecords + " 条记录");
            System.out.println("\n" + finalStats);
            
        } catch (Exception e) {
            logger.error("最终数据库更新失败: {}", e.getMessage(), e);
            System.out.println("警告: 最终数据库更新失败，但数据已写入: " + e.getMessage());
        }
    }
}