package org.example.xiaowudemo.transformData;

import org.apache.spark.sql.SparkSession;
import org.example.xiaowudemo.transformData.impl.AlipayDataTransformer;
import org.example.xiaowudemo.transformData.impl.BankDataTransformer;
import org.example.xiaowudemo.transformData.impl.GenericDataTransformer;
import org.example.xiaowudemo.transformData.impl.WechatDataTransformer;
import org.example.xiaowudemo.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 数据转换器工厂类，根据数据源类型创建对应的转换器
 */
public class DataTransformerFactory {
    private static final Logger logger = LoggerFactory.getLogger(DataTransformerFactory.class);

    /**
     * 创建数据转换器
     * @param sourceType 数据源类型
     * @param spark Spark会话
     * @param classifiedDir 分类目录
     * @param databaseService 数据库服务（可选）
     * @return 数据转换器
     */
    public static DataTransformer createTransformer(String sourceType, SparkSession spark, String classifiedDir, DatabaseService databaseService) {
        logger.info("创建数据转换器，数据源类型: {}", sourceType);
        boolean isBank = sourceType.toLowerCase().contains("银行") || 
                         sourceType.equalsIgnoreCase("bank");
        
        if (isBank) {
            logger.info("检测到银行数据，创建银行数据转换器");
            return new BankDataTransformer(spark, classifiedDir, databaseService);
        }

        return switch (sourceType.toLowerCase()) {
            case "支付宝" -> {
                logger.info("创建支付宝数据转换器");
                yield new AlipayDataTransformer(spark, classifiedDir, databaseService);
            }
            case "微信" -> {
                logger.info("创建微信数据转换器");
                yield new WechatDataTransformer(spark, classifiedDir, databaseService);
            }
            default -> {
                logger.info("创建通用数据转换器");
                yield new GenericDataTransformer(spark, classifiedDir, databaseService);
            }
        };
    }

    /**
     * 创建数据转换器（兼容旧版本，不使用数据库服务）
     * @param sourceType 数据源类型
     * @param spark Spark会话
     * @param classifiedDir 分类目录
     * @return 数据转换器
     */
    @Deprecated
    public static DataTransformer createTransformer(String sourceType, SparkSession spark, String classifiedDir) {
        return createTransformer(sourceType, spark, classifiedDir, null);
    }
} 