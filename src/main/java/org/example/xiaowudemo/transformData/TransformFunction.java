package org.example.xiaowudemo.transformData;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.math.BigDecimal;

public class TransformFunction {
    private static final Logger logger = Logger.getLogger(TransformFunction.class.getName());
    private static final Set<String> loggedUnknownValues = new HashSet<>();
    static {
        logger.setLevel(Level.INFO);
    }
    
    /*
    data_transform -> 支付宝的extract_name
    提取的是账户名称
     */
    public static String extractName(String field){
        if(field == null || field.trim().isEmpty()){
            return "";
        }
        
        // 查找括号内的名称
        if(field.contains("(") && field.contains(")")){
            int startIndex = field.indexOf("(") + 1;
            int endIndex = field.lastIndexOf(")"); // 使用最后一个右括号以处理嵌套括号
            
            if(startIndex < endIndex) {
                String name = field.substring(startIndex, endIndex).trim();
                if(!name.isEmpty()){
                    return name;
                }
            }
        }
        
        // 如果没有括号或提取失败，返回原始值
        return field;
    }
    
    /*
    data_transform -> 支付宝的extract_account
    提取的是账户
     */
    public static String extractAccount(String field)
    {
        if(field == null || field.trim().isEmpty()){
            return "";
        }
        if(field.contains("(")){
            String account = field.substring(0, field.indexOf("(")).trim();
            if(!account.isEmpty() && account.matches("\\d+")){ // 确保提取的是数字
                return account;
            }
        }
        // 如果字段不包含括号但是纯数字，也认为是账号
        return field;
    }
    
    /**
     * 处理银行卡号或账号的科学计数法问题及下划线问题
     * 将科学计数法表示的大数字转换为普通数字字符串
     * 并且处理包含下划线的卡号，只保留下划线前的部分
     */
    public static String normalizeCardNumber(String cardNumber) {
        if(cardNumber == null || cardNumber.trim().isEmpty()){
            return "";
        }
        
        String trimmed = cardNumber.trim();
        
        // 处理包含下划线的卡号（例如：6214993150030026_156_1）
        if(trimmed.contains("_")) {
            // 只保留下划线前的部分
            trimmed = trimmed.substring(0, trimmed.indexOf("_"));
            logger.fine("处理下划线卡号，提取结果: " + trimmed);
        }
        
        // 检查是否为科学计数法表示
        if(trimmed.contains("E") || trimmed.contains("e")) {
            try {
                // 使用BigDecimal处理科学计数法，保持精度
                BigDecimal bd = new BigDecimal(trimmed);
                return bd.toPlainString();
            } catch(NumberFormatException e) {
                // 降低日志级别，避免大量输出
                logger.fine("无法转换可能的科学计数法数值: " + trimmed);
                // 转换失败时返回原始值
                return trimmed;
            }
        }
        
        // 检查是否为带小数点的数字，但实际上是整数值（如123.0）
        if(trimmed.contains(".")) {
            try {
                BigDecimal bd = new BigDecimal(trimmed);
                if(bd.stripTrailingZeros().scale() <= 0) {
                    // 如果小数部分为0，返回整数部分
                    return bd.toBigInteger().toString();
                }
            } catch(NumberFormatException e) {
                // 忽略错误，继续使用原始值
            }
        }
        
        return trimmed;
    }
    
    public static String latest(String... times){
        if(times == null || times.length == 0)
        {
            return "";
        }
        
        try{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date latestDate = null;
            String latestTime = "";
            
            for (String time : times)
            {
                if (time != null && !time.isEmpty())
                {
                    try {
                        String cleanTime = time.trim();
                        // 处理Timestamp格式的字符串
                        if (cleanTime.contains(".")) {
                            cleanTime = cleanTime.substring(0, cleanTime.indexOf("."));
                        }
                        
                        Date date = sdf.parse(cleanTime);
                        if (latestDate == null || date.after(latestDate)){
                            latestDate = date;
                            latestTime = cleanTime;
                        }
                    } catch (ParseException e) {
                        // 继续处理下一个日期
                    }
                }
            }
            
            if (latestTime.isEmpty()) {
                // 如果没有找到有效日期，返回第一个非空值
                return Arrays.stream(times).filter(t -> t != null && !t.isEmpty()).findFirst().orElse("");
            }
            
            return latestTime;
        } catch (Exception e) {
            logger.warning("latest函数处理异常: " + e.getMessage());
            // 出现异常时，返回第一个非空值
            return Arrays.stream(times).filter(t -> t != null && !t.isEmpty()).findFirst().orElse("");
        }
    }
    
    public static String normalizeIncomeExpense(String value)
    {
        if(value == null)
        {
            return "";
        }
        
        // 清理输入值，移除空格和特殊字符
        String cleanValue = value.trim().replaceAll("[\\s\\t\\n]", "");
        
        // 如果清理后为空字符串，直接返回空
        if (cleanValue.isEmpty()) {
            return "";
        }
        
        // 转换为小写进行比较
        String lowerValue = cleanValue.toLowerCase();
        
        // ===== 支付宝格式处理 =====
        if (cleanValue.equals("收") || cleanValue.startsWith("收/") || cleanValue.equals("收入")) {
            return "进";
        } else if (cleanValue.equals("支") || cleanValue.endsWith("/支") || cleanValue.equals("支出")) {
            return "出";
        }
        
        // ===== 银行格式处理 =====
        // 借贷标志处理
        if (lowerValue.equals("借") || lowerValue.equals("借方") || lowerValue.equals("dr") || lowerValue.equals("debit")) {
            return "出";
        } else if (lowerValue.equals("贷") || lowerValue.equals("贷方") || lowerValue.equals("cr") || lowerValue.equals("credit")) {
            return "进";
        }
        
        // 进出/收支标志处理
        if (lowerValue.contains("收") || lowerValue.contains("入") || 
            lowerValue.equals("进") || lowerValue.equals("收款") || 
            lowerValue.contains("存") || lowerValue.contains("转入")) {
            return "进";
        } else if (lowerValue.contains("支") || lowerValue.contains("出") || 
                   lowerValue.equals("付") || lowerValue.equals("付款") || 
                   lowerValue.contains("取") || lowerValue.contains("转出")) {
            return "出";
        }
        
        // 符号处理
        if (lowerValue.equals("+") || lowerValue.equals("加")) {
            return "进";
        } else if (lowerValue.equals("-") || lowerValue.equals("减")) {
            return "出";
        }
        
        // ===== 微信格式处理 =====
        if (lowerValue.contains("收入") || lowerValue.contains("充值") || lowerValue.contains("退款")) {
            return "进";
        } else if (lowerValue.contains("支出") || lowerValue.contains("消费") || lowerValue.contains("提现")) {
            return "出";
        }
        
        // ===== 中国银行特殊格式 =====
        if (lowerValue.contains("进账") || lowerValue.contains("汇入") || lowerValue.contains("收方")) {
            return "进";
        } else if (lowerValue.contains("支取") || lowerValue.contains("汇出") || lowerValue.contains("付方")) {
            return "出";
        }
        
        // ===== 中国农业银行特殊格式 =====
        if (lowerValue.contains("income") || lowerValue.equals("i")) {
            return "进";
        } else if (lowerValue.contains("expense") || lowerValue.equals("e") || lowerValue.equals("o")) {
            return "出";
        }
        
        // 如果无法识别，并且该值尚未被记录过，则记录日志
        // 这样可以避免相同值的大量重复日志
        if (!loggedUnknownValues.contains(value)) {
            logger.warning("无法识别的收支标志: " + value);
            loggedUnknownValues.add(value);
            // 避免集合无限增长，当集合太大时清空
            if (loggedUnknownValues.size() > 100) {
                loggedUnknownValues.clear();
            }
        }
        
        return cleanValue;
    }
    
    public static String normalizeDate(String dateStr){
        if(dateStr == null || dateStr.trim().isEmpty()){
            return "";
        }
        try{
            String cleanDateStr = dateStr.trim();
            Date date = null;
            if(cleanDateStr.matches("\\d{8}")){
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd");
                date = inputFormat.parse(cleanDateStr);
            }else if(cleanDateStr.contains(":")){
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                if(cleanDateStr.contains(".")){
                    cleanDateStr = cleanDateStr.substring(0, cleanDateStr.indexOf("."));
                }
                date = inputFormat.parse(cleanDateStr);
            }else{
                try{
                    SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
                    date = format1.parse(cleanDateStr);
                }catch(ParseException e){
                    return dateStr;
                }
            }  
           SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
            return outputFormat.format(date);
        }catch(Exception e){
            logger.warning("日期格式化异常: " + e.getMessage());
            return dateStr;
        }
    }

    /**
     * 规范化货币金额
     * @param currency 包含货币符号或文字的字符串，或者表示分的数字
     * @return 清理后的数值字符串，如果无法解析则返回null
     */
    public static String normalizeCurrency(Object currency) {
        if (currency == null) {
            return null;
        }
        
        // 处理Double类型（微信的"分"转"元"）
        if (currency instanceof Double || currency instanceof Integer || currency instanceof Long) {
            try {
                // 将分转换为元（除以100）
                java.math.BigDecimal value = new java.math.BigDecimal(currency.toString()).divide(new java.math.BigDecimal("100"));
                return value.toPlainString();
            } catch (Exception e) {
                logger.warning("无法将数值 '" + currency + "' 从分转换为元: " + e.getMessage());
                return currency.toString();
            }
        }
        
        // 处理字符串类型
        String currencyString = currency.toString();
        if (currencyString.trim().isEmpty()) {
            return null;
        }
        
        // 移除货币符号 (¥, $, €等)，逗号，空格，以及中文"元"
        String cleaned = currencyString.replaceAll("[¥￥$,元€]", "") // 移除常见货币符号和"元"
                                   .replaceAll(",", "")          // 移除千位分隔符
                                   .replaceAll("\\s", "");      // 移除空格

        // 尝试将其解析为数值，验证格式
        try {
            // 使用 BigDecimal 来处理精度，但只返回字符串形式
            new java.math.BigDecimal(cleaned);
            return cleaned;
        } catch (NumberFormatException e) {
            // 如果清理后仍然不是有效的数字格式，则返回null或原始值，取决于需求
            logger.warning("无法将货币字符串 '" + currencyString + "' 标准化为数字，清理后为 '" + cleaned + "'");
            return currencyString;
        }
    }
}

