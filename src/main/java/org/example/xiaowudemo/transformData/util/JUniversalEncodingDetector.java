package org.example.xiaowudemo.transformData.util;

import org.mozilla.universalchardet.UniversalDetector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于JUniversalChardet的编码检测工具
 */
public class JUniversalEncodingDetector {
    private static final Logger logger = LoggerFactory.getLogger(JUniversalEncodingDetector.class);
    // 编码检测缓存
    private static final Map<String, String> encodingCache = new ConcurrentHashMap<>();
    private static final List<String> SUPPORTED_ENCODINGS = Arrays.asList(
            "UTF-8", "GBK", "GB18030", "GB2312", "ISO-8859-1"
    );
    /**
     * 检测文件的编码
     * @param filePath 文件路径
     * @return 检测到的编码，如果无法确定则返回UTF-8
     */
    public static String detectFileEncoding(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists() || !file.isFile()) {
                logger.warn("文件不存在或不是常规文件: {}", filePath);
                return "UTF-8";
            }
            // 检查缓存
            if (encodingCache.containsKey(filePath)) {
                String cachedEncoding = encodingCache.get(filePath);
                logger.debug("使用缓存的编码 {}: {}", cachedEncoding, filePath);
                return cachedEncoding;
            }
            String bomEncoding = detectBOM(filePath);
            if (bomEncoding != null) {
                logger.info("通过BOM检测到文件编码: {}", bomEncoding);
                cacheEncoding(filePath, bomEncoding);
                return bomEncoding;
            }
            // 使用JUniversalChardet检测编码
            String encoding = detectWithJUniversal(filePath);
            if (encoding != null) {
                if (verifyEncoding(filePath, encoding)) {
                    logger.info("JUniversalChardet检测到文件编码: {}", encoding);
                    cacheEncoding(filePath, encoding);
                    return encoding;
                }
            }
            // 如果JUniversalChardet检测失败或验证失败，使用我自身的备用的代码检测方法
            encoding = detectWithFallback(filePath);
            cacheEncoding(filePath, encoding);
            return encoding;
        } catch (Exception e) {
            logger.error("编码检测过程中发生异常: {}", e.getMessage(), e);
            return handleDetectionException(filePath, e);
        }
    }
    /**
     * 使用JUniversalChardet检测文件编码
     * @param filePath 文件路径
     * @return 检测到的编码，如果检测失败则返回null
     */
    private static String detectWithJUniversal(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            UniversalDetector detector = new UniversalDetector(null);
            byte[] buffer = new byte[8192];
            int bytesRead;
            long maxBytesToRead = 1024 * 1024;
            long totalBytesRead = 0;
            // 读取文件内容进行检测
            while ((bytesRead = fis.read(buffer)) > 0 && !detector.isDone() && totalBytesRead < maxBytesToRead) {
                detector.handleData(buffer, 0, bytesRead);
                totalBytesRead += bytesRead;
            }
            detector.dataEnd();
            String encoding = detector.getDetectedCharset();
            if (encoding == null) {
                logger.debug("JUniversalChardet无法检测文件编码: {}", filePath);
                return null;
            }
            logger.debug("JUniversalChardet检测到原始编码: {}", encoding);
            if (!SUPPORTED_ENCODINGS.contains(encoding)) {
                String mappedEncoding = mapToSupportedEncoding(encoding);
                logger.debug("将检测到的编码 {} 映射到支持的编码 {}: {}", encoding, mappedEncoding, filePath);
                return mappedEncoding;
            }
            return encoding;
        } catch (IOException e) {
            logger.warn("使用JUniversalChardet检测编码时出错: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 将检测到的编码映射到支持的编码
     * @param encoding 检测到的编码
     * @return 映射后的编码
     */
    private static String mapToSupportedEncoding(String encoding) {
        if (encoding == null) {
            return "UTF-8";
        }
        String normalizedEncoding = encoding.toUpperCase();
        // 编码映射规则
        if (normalizedEncoding.contains("GB") || 
            normalizedEncoding.contains("GBK") || 
            normalizedEncoding.contains("936") ||  // Windows-936 是 GBK
            normalizedEncoding.contains("CP936")) {
            return "GBK";
        } else if (normalizedEncoding.contains("GB18030") || 
                   normalizedEncoding.contains("54936")) {  // Windows-54936 是 GB18030
            return "GB18030";
        } else if (normalizedEncoding.contains("GB2312") || 
                   normalizedEncoding.contains("EUC-CN")) {
            return "GB2312";
        } else if (normalizedEncoding.contains("UTF") || 
                   normalizedEncoding.contains("UNICODE") ||
                   normalizedEncoding.contains("65001")) {  // Windows-65001 是 UTF-8
            return "UTF-8";
        } else if (normalizedEncoding.contains("ISO") || 
                   normalizedEncoding.contains("8859") ||
                   normalizedEncoding.contains("LATIN")) {
            return "ISO-8859-1";
        } else if (normalizedEncoding.contains("BIG5") || 
                   normalizedEncoding.contains("950")) {
            return "GBK";
        }
        
        // 默认返回UTF-8
        logger.debug("未知编码 {} 映射到默认的UTF-8", encoding);
        return "UTF-8";
    }
    
    /**
     * 检测文件是否包含BOM标记
     * @param filePath 文件路径
     * @return 如果检测到BOM，返回对应的编码，否则返回null
     */
    private static String detectBOM(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            byte[] bom = new byte[4];
            int read = fis.read(bom, 0, 4);
            
            if (read >= 3 && bom[0] == (byte)0xEF && bom[1] == (byte)0xBB && bom[2] == (byte)0xBF) {
                return "UTF-8";
            } else if (read >= 2 && bom[0] == (byte)0xFE && bom[1] == (byte)0xFF) {
                return "UTF-16BE";
            } else if (read >= 2 && bom[0] == (byte)0xFF && bom[1] == (byte)0xFE) {
                return "UTF-16LE";
            } else if (read >= 4 && bom[0] == (byte)0x00 && bom[1] == (byte)0x00 && 
                       bom[2] == (byte)0xFE && bom[3] == (byte)0xFF) {
                return "UTF-32BE";
            }
        } catch (IOException e) {
            logger.warn("检测BOM时出错: {}", e.getMessage());
        }
        return null;
    }
    
    /**
     * 验证检测到的编码是否正确
     * @param filePath 文件路径
     * @param encoding 要验证的编码
     * @return 如果编码有效返回true，否则返回false
     */
    private static boolean verifyEncoding(String filePath, String encoding) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(filePath), encoding))) {
            String line;
            int lineCount = 0;
            while ((line = reader.readLine()) != null && lineCount < 5) {
                if (containsGarbledText(line)) {
                    return false;
                }
                lineCount++;
            }
            return true;
        } catch (IOException e) {
            logger.warn("验证编码 {} 时出错: {}", encoding, e.getMessage());
            return false;
        }
    }
    /**
     * 使用备选方法检测文件编码
     * @param filePath 文件路径
     * @return 检测到的编码
     */
    private static String detectWithFallback(String filePath) {
        logger.debug("使用备选方法检测文件编码: {}", filePath);
        String contentType = detectContentType(filePath);
        if (contentType != null) {
            logger.info("根据内容类型推断编码: {}", contentType);
            if (contentType.contains("中文") || contentType.contains("Chinese")) {
                // 如果检测到中文内容，优先尝试中文编码
                for (String encoding : new String[]{"GBK", "GB18030", "GB2312", "UTF-8"}) {
                    if (verifyEncoding(filePath, encoding)) {
                        logger.info("根据中文内容类型检测到编码: {}", encoding);
                        return encoding;
                    }
                }
            }
        }
        // 按照支持的编码列表顺序尝试
        for (String encoding : SUPPORTED_ENCODINGS) {
            if (verifyEncoding(filePath, encoding)) {
                logger.info("备选方法检测到文件编码: {}", encoding);
                return encoding;
            }
        }
        logger.info("所有编码检测方法都失败，使用默认UTF-8编码: {}", filePath);
        return "UTF-8";
    }
    
    /**
     * 检测文件的内容类型
     * @param filePath 文件路径
     * @return 内容类型描述，如果无法确定则返回null
     */
    private static String detectContentType(String filePath) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(filePath), StandardCharsets.UTF_8))) {
            // 读取前10行
            StringBuilder content = new StringBuilder();
            String line;
            int lineCount = 0;
            while ((line = reader.readLine()) != null && lineCount < 10) {
                content.append(line).append("\n");
                lineCount++;
            }
            
            String contentStr = content.toString();
            if (contentStr.matches(".*[\\u4e00-\\u9fa5]+.*")) {
                return "中文内容";
            }
            // 检查是否主要是ASCII字符
            int asciiCount = 0;
            for (int i = 0; i < contentStr.length(); i++) {
                if (contentStr.charAt(i) < 128) {
                    asciiCount++;
                }
            }
            if (asciiCount > contentStr.length() * 0.9) {
                return "ASCII内容";
            }
            return null;
        } catch (IOException e) {
            logger.debug("检测内容类型时出错: {}", e.getMessage());
            return null;
        }
    }
    /**
     * 检查文本是否包含乱码
     * @param text 要检查的文本
     * @return 如果包含乱码返回true，否则返回false
     */
    private static boolean containsGarbledText(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        if (text.contains("�")) {
            return true;
        }
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c < 32 && c != 9 && c != 10 && c != 13) {
                return true;
            }
        }
        if (text.contains("\uFFFD") || 
            text.contains("锟") ||
            text.contains("锔") ||
            text.chars().anyMatch(c -> c == 65533)) {
            return true;
        }
        if (text.matches(".*[\\u4e00-\\u9fa5][\\u0000-\\u007F]+[\\u4e00-\\u9fa5].*") && 
            text.length() < 10) {
            // 中文字符之间夹杂ASCII字符，且长度较短，可能是乱码
            return true;
        }
        int unusualChars = 0;
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            // 检查一些不常用的Unicode区域
            if ((c >= 0xE000 && c <= 0xF8FF) || // 私有区域
                (c >= 0x2600 && c <= 0x27BF) || // 杂项符号
                (c >= 0x3000 && c <= 0x303F && c != 0x3001 && c != 0x3002)) { // 中日韩符号和标点（排除常用的顿号和句号）
                unusualChars++;
            }
        }
        // 如果不常见字符比例过高，可能是乱码
        return unusualChars > text.length() * 0.3;
    }
    
    /**
     * 将编码保存到缓存中
     * @param filePath 文件路径
     * @param encoding 编码
     */
    public static void cacheEncoding(String filePath, String encoding) {
        encodingCache.put(filePath, encoding);
        logger.info("已将文件 {} 的编码 {} 添加到缓存", filePath, encoding);
    }
    
    /**
     * 清除编码缓存
     */
    public static void clearCache() {
        encodingCache.clear();
        logger.info("编码检测缓存已清除");
    }
    /**
     * 获取缓存中的编码
     * @param filePath 文件路径
     * @return 缓存的编码，如果不存在则返回null
     */
    public static String getCachedEncoding(String filePath) {
        return encodingCache.get(filePath);
    }
    /**
     * 处理编码检测过程中的异常
     * @param filePath 文件路径
     * @param exception 异常
     * @return 默认编码
     */
    private static String handleDetectionException(String filePath, Exception exception) {
        logger.warn("编码检测失败，使用默认UTF-8编码: {}", filePath);
        if (exception instanceof SecurityException || 
            exception instanceof FileNotFoundException ||
            exception.getMessage().contains("Permission denied") ||
            exception.getMessage().contains("Access is denied")) {
            logger.error("文件访问权限问题: {}", filePath);
        }
        if (exception instanceof IOException && 
            (exception.getMessage().contains("Invalid") || 
             exception.getMessage().contains("Corrupt") ||
             exception.getMessage().contains("格式"))) {
            logger.error("可能的文件格式问题: {}", filePath);
        }
        
        return "UTF-8";
    }
} 