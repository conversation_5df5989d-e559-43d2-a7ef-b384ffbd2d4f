package org.example.xiaowudemo.transformData.util;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.functions;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 转换器工具类，提供通用的数据转换方法
 */
public class TransformerUtils {
    
    /**
     * 构建表达式列
     * @param df 数据集
     * @param sourceExpr 源表达式
     * @param targetCol 目标列名
     * @param logger 日志记录器
     * @return 构建的列表达式
     */
    public static Column buildExpressionColumn(Dataset<Row> df, String sourceExpr, String targetCol, Logger logger) {
        // 处理字面值（带引号）
        if ((sourceExpr.startsWith("'") && sourceExpr.endsWith("'")) || 
            (sourceExpr.startsWith("\"") && sourceExpr.endsWith("\""))) {
            String literalValue = sourceExpr.substring(1, sourceExpr.length() - 1);
            return functions.lit(literalValue).as(targetCol);
        }
        if (sourceExpr.trim().isEmpty()) {
            logger.info("发现空表达式，使用空字符串代替: {}", targetCol);
            return functions.lit("").cast("string").as(targetCol);
        }
        if (sourceExpr.contains("(") && sourceExpr.endsWith(")")) {
            return handleFunctionCall(df, sourceExpr, targetCol, logger);
        }
        if (sourceExpr.contains(",") && !sourceExpr.contains("(")) {
            return handleMultipleFields(df, sourceExpr, targetCol, logger);
        }
        return handleColumnReference(df, sourceExpr, targetCol, logger);
    }
    
    /**
     * 处理函数调用
     *
     * @param df 数据集
     * @param sourceExpr 源表达式
     * @param targetCol 目标列名
     * @param logger 日志记录器
     * @return 构建的列表达式
     */
    public static Column handleFunctionCall(Dataset<Row> df, String sourceExpr, String targetCol, Logger logger) {
        if (sourceExpr.startsWith("normalize_income_expense(")) {
            String fieldName = sourceExpr.substring(24, sourceExpr.length() - 1).trim();
            if (Arrays.asList(df.columns()).contains(fieldName)) {
                return functions.callUDF("normalize_income_expense", functions.col(fieldName)).as(targetCol);
            } else {
                for (String col : df.columns()) {
                    if (isColumnMatch(col, fieldName)) {
                        logger.debug("找到相似列名: '{}' 匹配 '{}'", col, fieldName);
                        return functions.callUDF("normalize_income_expense", functions.col(col)).as(targetCol);
                    }
                }
            }
        }
        else if (sourceExpr.startsWith("extract_name(")) {
            String fieldName = sourceExpr.substring(12, sourceExpr.length() - 1).trim();
            if (Arrays.asList(df.columns()).contains(fieldName)) {
                return functions.callUDF("extract_name", functions.col(fieldName)).as(targetCol);
            } else {
                for (String col : df.columns()) {
                    if (isColumnMatch(col, fieldName)) {
                        logger.debug("找到相似列名: '{}' 匹配 '{}'", col, fieldName);
                        return functions.callUDF("extract_name", functions.col(col)).as(targetCol);
                    }
                }
            }
        }
        else if (sourceExpr.startsWith("extract_account(")) {
            String fieldName = sourceExpr.substring(15, sourceExpr.length() - 1).trim();
            if (Arrays.asList(df.columns()).contains(fieldName)) {
                return functions.callUDF("extract_account", functions.col(fieldName)).as(targetCol);
            } else {
                for (String col : df.columns()) {
                    if (isColumnMatch(col, fieldName)) {
                        logger.debug("找到相似列名: '{}' 匹配 '{}'", col, fieldName);
                        return functions.callUDF("extract_account", functions.col(col)).as(targetCol);
                    }
                }
            }
        }
        else if (sourceExpr.startsWith("latest(")) {
            String paramsStr = sourceExpr.substring(7, sourceExpr.length() - 1);
            String[] params = paramsStr.split(",");
            List<Column> paramColumns = new ArrayList<>();
            for (String param : params) {
                param = param.trim();
                if (Arrays.asList(df.columns()).contains(param)) {
                    paramColumns.add(functions.col(param));
                } else {
                    boolean found = false;
                    for (String col : df.columns()) {
                        if (isColumnMatch(col, param)) {
                            logger.debug("找到相似列名: '{}' 匹配 '{}'", col, param);
                            paramColumns.add(functions.col(col));
                            found = true;
                            break;
                        }
                    }
                    
                    if (!found) {
                        paramColumns.add(functions.lit(null));
                    }
                }
            }
            if (!paramColumns.isEmpty()) {
                return functions.callUDF("latest", functions.array(paramColumns.toArray(new Column[0]))).as(targetCol);
            }
        }
        else {
            int leftParenIndex = sourceExpr.indexOf("(");
            if (leftParenIndex > 0) {
                String funcName = sourceExpr.substring(0, leftParenIndex).trim();
                String paramsStr = sourceExpr.substring(leftParenIndex + 1, sourceExpr.length() - 1).trim();
                logger.info("尝试处理未知函数调用: {}({})", funcName, paramsStr);
                if (!paramsStr.contains(",")) {
                    if (Arrays.asList(df.columns()).contains(paramsStr)) {
                        return functions.callUDF(funcName, functions.col(paramsStr)).as(targetCol);
                    } else {
                        for (String col : df.columns()) {
                            if (isColumnMatch(col, paramsStr)) {
                                logger.debug("找到相似列名: '{}' 匹配 '{}'", col, paramsStr);
                                return functions.callUDF(funcName, functions.col(col)).as(targetCol);
                            }
                        }
                    }
                }
                else {
                    logger.warn("不支持的多参数函数调用: {}", sourceExpr);
                }
            }
        }
        logger.warn("无法处理的函数调用: {}", sourceExpr);
        return functions.lit("").cast("string").as(targetCol);
    }
    /**
     * 处理多个字段合并
     * @param df 数据集
     * @param sourceExpr 源表达式
     * @param targetCol 目标列名
     * @param logger 日志记录器
     * @return 构建的列表达式
     */
    public static Column handleMultipleFields(Dataset<Row> df, String sourceExpr, String targetCol, Logger logger) {
        String[] fields = sourceExpr.split(",");
        List<Column> concatColumns = new ArrayList<>();
        for (String field : fields) {
            field = field.trim();
            if (Arrays.asList(df.columns()).contains(field)) {
                concatColumns.add(functions.col(field));
            } else {
                boolean found = false;
                for (String col : df.columns()) {
                    if (isColumnMatch(col, field)) {
                        logger.debug("找到相似列名: '{}' 匹配 '{}'", col, field);
                        concatColumns.add(functions.col(col));
                        found = true;
                        break;
                    }
                }
                
                if (!found) {
                    concatColumns.add(functions.lit(""));
                }
            }
        }
        if (!concatColumns.isEmpty()) {
            return functions.concat_ws(", ", concatColumns.toArray(new Column[0])).as(targetCol);
        }
        
        return functions.lit("").as(targetCol);
    }
    /**
     * 处理列引用
     * @param df 数据集
     * @param sourceExpr 源表达式
     * @param targetCol 目标列名
     * @param logger 日志记录器
     * @return 构建的列表达式
     */
    public static Column handleColumnReference(Dataset<Row> df, String sourceExpr, String targetCol, Logger logger) {
        if (Arrays.asList(df.columns()).contains(sourceExpr)) {
            return functions.col(sourceExpr).as(targetCol);
        } else {
            for (String col : df.columns()) {
                if (isColumnMatch(col, sourceExpr)) {
                    logger.debug("找到相似列名: '{}' 匹配 '{}'", col, sourceExpr);
                    return functions.col(col).as(targetCol);
                }
            }
        }
        
        logger.warn("找不到匹配的列: '{}', 将使用空字符串代替", sourceExpr);
        return functions.lit("").cast("string").as(targetCol);
    }
    
    /**
     * 判断列名是否匹配
     * @param columnName 列名
     * @param pattern 匹配模式
     * @return 是否匹配
     */
    public static boolean isColumnMatch(String columnName, String pattern) {
        if (columnName.equalsIgnoreCase(pattern)) {
            return true;
        }
        String colLower = columnName.toLowerCase();
        String patternLower = pattern.toLowerCase();
        if (colLower.contains(patternLower) || patternLower.contains(colLower)) {
            if (patternLower.length() >= 3 || colLower.length() >= 3) {
                if (colLower.contains(patternLower)) {
                    int index = colLower.indexOf(patternLower);
                    int endIndex = index + patternLower.length();
                    boolean isAtStart = (index == 0);
                    boolean isAtEnd = (endIndex == colLower.length());
                    boolean isDelimitedBefore = (index > 0 && !Character.isLetterOrDigit(colLower.charAt(index - 1)));
                    boolean isDelimitedAfter = (endIndex < colLower.length() && !Character.isLetterOrDigit(colLower.charAt(endIndex)));
                    
                    return isAtStart || isAtEnd || (isDelimitedBefore && isDelimitedAfter);
                }
                else if (patternLower.contains(colLower)) {
                    return (double)colLower.length() / patternLower.length() >= 0.5;
                }
                return true;
            }
        }
        return false;
    }
} 