package org.example.xiaowudemo.transformData.UDFRegistryFunction;

import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.api.java.UDF1;
import org.apache.spark.sql.types.DataTypes;
import org.example.xiaowudemo.transformData.TransformFunction;

import scala.collection.Seq;
import java.util.ArrayList;
import java.util.List;
import java.sql.Timestamp;

/**
 * UDF注册器 - 负责向Apache Spark会话注册用户自定义函数(User Defined Functions)
 * 
 * 主要职责：
 * 1. 将Java方法包装为Spark UDF，使其能在Spark SQL查询中使用
 * 2. 定义UDF的输入参数类型、返回值类型和函数名称
 * 3. 为银行数据清洗工具提供专用的数据转换函数
 * 
 * 核心名词解释：
 * - UDF (User Defined Function): 用户自定义函数，允许在Spark SQL中使用自定义的Java/Scala函数
 * - SparkSession: Spark应用程序的入口点，用于创建DataFrame和执行SQL查询
 * - DataTypes: Spark SQL的数据类型定义，用于指定函数的输入输出类型
 * - Lambda表达式: Java 8的函数式编程特性，用于简化匿名函数的定义
 */
public class UDFRegistry {
    
    /**
     * 向指定的Spark会话注册所有用户自定义函数
     * 
     * @param spark Spark会话对象，用于执行UDF注册操作
     */
    public static void register(SparkSession spark){
        
        // 注册姓名提取函数 - 从格式化字符串中提取人员姓名
        // 用途：处理银行数据中"账户号(姓名)"格式的字段，提取出纯净的姓名信息
        // 输入类型：String，输出类型：String
        spark.udf().register("extract_name", 
            (UDF1<String, String>) TransformFunction::extractName, 
            DataTypes.StringType);
        
        // 注册账户提取函数 - 从格式化字符串中提取账户号码
        // 用途：处理银行数据中"账户号(姓名)"格式的字段，提取出纯净的账户号码
        // 输入类型：String，输出类型：String
        spark.udf().register("extract_account", 
            (UDF1<String, String>) TransformFunction::extractAccount, 
            DataTypes.StringType);
        
        // 注册最新时间选择函数 - 从多个时间字段中选择最新的时间戳
        // 用途：当多个时间字段存在时，自动选择最新的时间作为有效时间
        // 输入类型：Seq<?>（Scala序列，包含任意类型的时间对象），输出类型：String
        spark.udf().register("latest", 
            (UDF1<Seq<?>, String>) (times) -> {
                // 创建Java字符串列表，用于存储转换后的时间字符串
                List<String> timeList = new ArrayList<>();
                
                // 将Scala序列转换为Java列表并遍历每个时间项
                scala.collection.JavaConverters.seqAsJavaListConverter(times).asJava().forEach(item -> {
                    // 空值检查：确保时间项不为null，避免NullPointerException
                    if (item != null) {
                        // 类型检查：使用instanceof操作符检查当前项是否属于java.sql.Timestamp类型
                        // 如果是Timestamp类型，则直接调用toString()方法获取标准时间格式
                        if (item instanceof Timestamp ts) {
                            timeList.add(ts.toString());
                        } else {
                            // 如果不是Timestamp类型（可能是字符串或其他时间类型），
                            // 则调用通用的toString()方法进行字符串转换
                            timeList.add(item.toString());
                        }
                    }
                });
                
                // 将Java列表转换为字符串数组，传递给TransformFunction.latest()方法处理
                String[] timeArray = timeList.toArray(new String[0]);
                return TransformFunction.latest(timeArray);
            }, 
            DataTypes.StringType);
        
        // 注册收支标准化函数 - 统一收入支出标识符的格式
        // 用途：将不同数据源中的收支标识（如"收入"、"支出"、"+"、"-"等）统一为标准格式
        // 输入类型：String，输出类型：String
        spark.udf().register("normalize_income_expense", 
            (UDF1<String, String>) TransformFunction::normalizeIncomeExpense, 
            DataTypes.StringType);
        
        // 注册货币标准化函数 - 统一货币金额的格式和表示方式
        // 用途：处理不同格式的货币数据（如带符号、带逗号分隔符等），转换为统一的数值格式
        // 输入类型：Object（支持多种数据类型输入），输出类型：String
        spark.udf().register("normalize_currency", 
            (UDF1<Object, String>) TransformFunction::normalizeCurrency, 
            DataTypes.StringType);
    }
}
