package org.example.xiaowudemo.transformData;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.expressions.Window;
import org.apache.spark.sql.expressions.WindowSpec;
import org.apache.spark.sql.expressions.UserDefinedFunction;
import org.apache.spark.sql.types.DataTypes;
import org.example.xiaowudemo.transformData.UDFRegistryFunction.UDFRegistry;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.example.xiaowudemo.transformData.util.TransformerUtils;
import org.example.xiaowudemo.util.FileReaderUtil;
import org.example.xiaowudemo.service.DatabaseService;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import org.apache.spark.sql.functions;
/**
 * 数据转换器抽象基类，实现通用的转换逻辑
 */
public abstract class AbstractDataTransformer implements DataTransformer {
    protected final SparkSession spark;
    protected final String classifiedDir;
    protected final DatabaseService databaseService;
    protected static final Logger logger = LoggerFactory.getLogger(AbstractDataTransformer.class);
    
    /**
     * 构造函数
     * @param spark Spark会话
     * @param classifiedDir 分类目录
     * @param databaseService 数据库服务
     */
    public AbstractDataTransformer(SparkSession spark, String classifiedDir, DatabaseService databaseService) {
        this.spark = spark;
        this.classifiedDir = classifiedDir;
        this.databaseService = databaseService;
        registerCommonUDFs();
        registerSourceSpecificUDFs();
    }
    /**
     * 注册通用UDF函数
     */
    protected void registerCommonUDFs() {
        UDFRegistry.register(spark);
    }
    /**
     * 注册特定数据源的UDF函数，由子类实现
     */
    protected abstract void registerSourceSpecificUDFs();
    /**
     * 从目录中读取指定格式的文件
     * @param directory 源目录路径
     * @param format 文件格式
     * @return 数据集
     * @throws Exception 如果读取失败
     */
    protected Dataset<Row> readFilesFromDirectory(String directory, String format) throws Exception {
        logger.info("读取目录: {}", directory);
        // 完全委托给 FileReaderUtil 处理文件读取
        Dataset<Row> df = FileReaderUtil.readDirectory(spark, directory, format);
        if (df.isEmpty()) {
            logger.warn("目录中没有找到{}文件或读取失败: {}", format, directory);
        }
        if (df.isEmpty()) {
            logger.warn("数据集为空，跳过处理");
            return df; // Return the empty DataFrame
        }
        Row firstRow = df.first();
        String[] header = df.columns();
        boolean isHeaderOnly = true;
        if (firstRow.length() == header.length) {
            for (int i = 0; i < header.length; i++) {
                Object value = firstRow.get(i);
                if (value == null || !String.valueOf(value).equals(header[i])) {
                    isHeaderOnly = false;
                    break;
                }
            }
        } else {
            isHeaderOnly = false;
        }

        if (isHeaderOnly) {
            logger.warn("数据集仅包含头部，返回空数据集");
            return spark.createDataFrame(new ArrayList<>(), df.schema());
        }

        return df;
    }
    /**
     * 清理列名（移除空白字符、特殊字符等）
     * @param df 输入数据集
     * @return 清理后的数据集
     */
    protected Dataset<Row> cleanColumnNames(Dataset<Row> df) {
       logger.info("开始清洗列名");
       String[] columns = df.columns();
       Map<String,String> columnMappings = new HashMap<>();
       for(String col : columns)
       {
           String cleanedCol = col.replaceAll("\\s+","")
                   .replaceAll("\t","")
                   .replaceAll(",","_")
                                 .trim();
           columnMappings.put(col,cleanedCol);
       }
       for(Map.Entry<String,String> entry : columnMappings.entrySet()){
           if(!entry.getKey().equals(entry.getValue()))
           {
               logger.info("重命名列:'{}'->'{}'",entry.getKey(),entry.getValue());
               df = df.withColumnRenamed(entry.getKey(),entry.getValue());
            }
        }
        return df;
    }
    /**
     * 执行数据转换
     * @param sourceType 数据源类型
     * @throws Exception 转换过程中可能发生的异常
     */
    @Override
    public void transform(String sourceType) throws Exception {
        String[] supportedFormats = {"csv","xlsx","txt"};
        boolean foundFiles = false; //标记是否找到文件，默认为false;
        logger.info("开始转换数据类型: {}", sourceType);
        for(String format : supportedFormats) {
            String sourcePath = classifiedDir + "/" + sourceType + "/" + format;
            logger.info("检查目录: {}", sourcePath);
            File sourceDir = new File(sourcePath);
            if (sourceDir.exists() && sourceDir.isDirectory()) {
                File[] allFiles = sourceDir.listFiles();
                List<File> files = new ArrayList<>();
                if (allFiles != null) {
                    for(File file : allFiles)
                    {
                        String fileName  = file.getName().toLowerCase();
                        if((format.equals("csv") && fileName.endsWith(".csv"))
                          ||(format.equals("xlsx") && fileName.endsWith(".xlsx")
                          ||(format.equals("txt") && fileName.endsWith(".txt"))
                        )){
                            files.add(file);
                            logger.info("找到文件:{}",file.getAbsolutePath());
                        }
                    }
                }
                if (!files.isEmpty()) {
                    foundFiles = true;
                    logger.info("在 {} 目录中找到 {} 个 {} 文件", sourcePath, files.size(), format);
                    try {
                        ObjectMapper mapper = new ObjectMapper();
                        JsonNode templatesNode = mapper.readTree(new File("src/main/resources/data_transform.json")).get("templates");
                        JsonNode template = StreamSupport.stream(templatesNode.spliterator(),false)
                                .filter(node -> sourceType.equals(node.get("source").asText()))
                                .findFirst()
                                .orElseThrow(()->new IllegalArgumentException("没有找到对应的模板:"+sourceType));
                        registerSourceSpecificUDFs();
                        Map<String,String> columnMappings = buildMappings(template.get("mapping"));
                        doTransform(sourceType,sourcePath,columnMappings,template,format);
                    } catch (FileNotFoundException e) {
                        logger.error("文件不存在: {}, 错误: {}", sourcePath, e.getMessage());
                    } catch (Exception e) {
                        logger.error("处理 {} 格式文件时发生错误: {}", format, e.getMessage());
                        throw e;
                    }
                } else {
                    logger.info("在 {} 目录中没有找到 {} 文件", sourcePath, format);
                }
            } else {
                logger.info("目录 {} 不存在或不是一个目录", sourcePath);
            }
        }
        if (!foundFiles) {
            throw new IllegalArgumentException("在 " + classifiedDir + "/" + sourceType + " 目录下未找到支持的文件格式 (csv, xlsx, txt)");
        }
    }
    /**
     * 构建列映射
     * @param mappingNode 映射节点
     * @return 列映射
     */
    protected Map<String, String> buildMappings(JsonNode mappingNode) {
        Map<String,String> mappings = new HashMap<>();
        mappingNode.fields().forEachRemaining(entry ->{
            String value = entry.getValue().asText();
            if(value.startsWith("'")&& !value.contains("("))
            {
                value = value.substring(1); // 如果文件以引号开头且不是函数的话去掉开头的单引号
            }
            if(value.contains("("))
            {
                int openParenIndex = value.indexOf("(");
                int closeParenIndex = value.indexOf(")");
                if(openParenIndex > 0 && closeParenIndex > openParenIndex)
                {
                    String funcName = value.substring(0,openParenIndex + 1);//提取函数名和左括号
                    String params = value.substring(openParenIndex + 1,closeParenIndex); // 提取参数
                    String[] paramList = params.split(",");
                    for(int i = 0 ;i < paramList.length; i++){
                        String param = getString(paramList[i]);
                        paramList[i] = param;
                    }
                    value = funcName + String.join(",",paramList)+")";
                }
            }
            logger.info("映射:{} -> {}" ,entry.getKey(),value);
            mappings.put(entry.getKey(),value);
        });
        return mappings;
    }

    private @NotNull String getString(String paramList) {
        String param = paramList.trim();
        if(param.startsWith("'")){
            param = param.substring(1);
        }
        if(param.endsWith("'")){
            param = param.substring(0,param.length()-1);
        }
        if(param.startsWith("(") && !param.contains(","))
        {
            param = param.substring(1);
        }
        if(param.endsWith(")")&&!param.contains(","))
        {
            param = param.substring(0,param.length()-1);
        }
        return param;
    }

    /**
     * 执行数据转换的模板方法，定义了转换的骨架
     * @param sourceType 数据源类型
     * @param sourcePath 源路径
     * @param columnMappings 列映射
     * @param template 模板
     * @param fileFormat 文件格式
     * @throws Exception 如果转换失败
     */
    protected void doTransform(String sourceType, String sourcePath, Map<String, String> columnMappings, JsonNode template, String fileFormat) throws Exception {
        logger.info("开始执行转换逻辑，数据源类型:{}, 文件格式:{}", sourceType, fileFormat);
        File sourceDir = new File(sourcePath);
        if(!sourceDir.exists()){
            logger.error("源目录不存在:{}",sourcePath);
            throw new IllegalArgumentException("源目录不存在:" + sourcePath);
        }
        if(!sourceDir.isDirectory()){
            logger.error("指定的路径不是目录:{}",sourcePath);
            throw new IllegalArgumentException("指定的路径不是目录：" + sourcePath);
        }
        if(!sourceDir.canRead()){
            logger.error("没有读取源目录的权限:{}",sourcePath);
            throw new SecurityException("没有读取源目录的权限:" + sourcePath);
        }
        try {
            Dataset<Row> df = readFilesFromDirectory(sourcePath, fileFormat); // 读取文件数据
            if (df.isEmpty()) {
                logger.warn("未能读取到任何数据，跳过处理");
                return;
            }

            // --- 添加源文件路径和行号 ---
            // Use monotonically_increasing_id for a unique ID before window function for stability
            df = df.withColumn("__monotonic_id__", functions.monotonically_increasing_id());
            WindowSpec windowSpec = Window.partitionBy("__source_file_path__").orderBy("__monotonic_id__");
            // Add source file path
            df = df.withColumn("__source_file_path__", functions.input_file_name());

            Map<String, Long> cleanStats = new HashMap<>();
            // Recalculate original count AFTER adding helper columns if needed, although it shouldn't change row count
            cleanStats.put("原始行数", df.count()); // Count should be accurate here

            df.show(5,false);
            logger.debug("原始列名");
            for(String col : df.columns()){
                logger.debug("列名:'{}'",col);
            }
            df = cleanColumnNames(df);
            logger.debug("清理后的列名");
            for(String col : df.columns())
            {
                logger.debug("列名:'{}'",col);
            }

            // --- 新增：删除所有字符串列中的制表符并清理空白 (在列名清理后) ---
            logger.info("开始删除字符串列中的制表符并清理空白 (清理后)...");

            // 使用当前DataFrame的实际列名（已经清理后的列名）
            String[] actualColumns = df.columns();
            for (String colName : actualColumns) {
                // 检查该列是否为字符串类型
                if (df.schema().apply(colName).dataType().equals(DataTypes.StringType)) {
                    try {
                        // 使用反引号包裹列名，确保安全引用，避免斜杠等特殊字符被解释为路径
                        Column safeCol = functions.col("`" + colName + "`");

                        // 1. 替换制表符为空字符串
                        // 2. trim去除首尾空格
                        // 3. 将处理后为空的字符串转为null
                        Column processedCol = functions.regexp_replace(safeCol, "\\t", "");
                        processedCol = functions.trim(processedCol);
                        df = df.withColumn(colName,
                            functions.when(functions.length(processedCol).equalTo(0), functions.lit(null).cast(DataTypes.StringType))
                            .otherwise(processedCol)
                        );

                        logger.debug("成功处理列: {}", colName);
                    } catch (Exception e) {
                        logger.warn("处理列 '{}' 时发生错误: {}，跳过此列", colName, e.getMessage());
                        // 继续处理其他列，不让单个列的错误影响整体处理
                    }
                }
            }

            logger.info("删除制表符并清理空白完成 (清理后)。");
            // --- 新增结束 ---

            df.cache();
            // 估算数据量，设置合适的分区数
            long rowCount = df.count();
            int optimalPartitions = Math.max(1,(int)Math.ceil(rowCount/1_000_000.0));//100万行一个分区
            logger.info("数据集行数:{}，设置分区数:{}",rowCount,optimalPartitions);
            spark.conf().set("spark.sql.shuffle.partitions", optimalPartitions);
            // 处理数据
            df = preProcess(df,sourceType);//前处理，包括收付标志清洗
            long preProcessedCount = df.count();
            cleanStats.put("前处理后行数",preProcessedCount);
            df = processData(df,sourceType,columnMappings);//处理数据，映射列
            df = postProcess(df, sourceType);//后处理，添加交易类型
            cleanStats.put("最终保留行数",df.count());
            // 处理NULL/VOID类型列
            for (String colName : df.columns()) {
                if (df.schema().apply(colName).dataType().typeName().equals("null") ||
                    df.schema().apply(colName).dataType().typeName().equals("void")) {
                    logger.warn("发现VOID/NULL类型列: {}, 替换为字符串类型", colName);
                    df = df.withColumn(colName, functions.lit("").cast("string"));
                }
            }
            // 记录清洗统计信息
            cleanStats.put("移除的总行数",cleanStats.get("原始行数") - cleanStats.get("最终保留行数"));
            logCleaningStats(sourceType, fileFormat, cleanStats);
            // 使用默认分区策略
            df = df.repartition(optimalPartitions);
            String outputPath = classifiedDir + "/transformed/" + sourceType;
            logger.info("保存转换结果到: {}", outputPath);
            // 获取读取时使用的编码
            // 由于现在每个文件可能使用不同的编码，我们需要选择一个主要编码用于写入
            // 这里我们优先使用UTF-8，因为它支持最广泛的字符集
            String usedEncoding = "UTF-8";
            Map<String, String> fileEncodings = new HashMap<>();
            scala.collection.Map<String,String> allConf = spark.conf().getAll();
            Map<String, String> javaMap = scala.collection.JavaConverters.mapAsJavaMapConverter(allConf).asJava();//转为Java Map
            for (Map.Entry<String,String> entry : javaMap.entrySet()){
                String key = entry.getKey();
                if(key.startsWith("csv.input.encoding.") || key.startsWith("txt.input.encoding.")){
                    String fileName = key.substring(key.indexOf(".encoding.")+10);
                    fileEncodings.put(fileName,entry.getValue());
                    logger.info("文件{}使用编码:{}",fileName,entry.getValue());
                }
            }
            fileEncodings.values().stream() //使用Stream找最常用的编码。困...
                    .collect(Collectors.groupingBy(e -> e,Collectors.counting())) // 统计每种编码次数
                    .entrySet().stream()
                    .max(Map.Entry.comparingByValue()) //找最多的
                    .filter(e -> e.getValue() > fileEncodings.size() * 0.7)
                    .map(Map.Entry :: getKey);
            logger.info("写入编码:{}",usedEncoding);
            // 保存Parquet文件（可选，用于备份）
            df.write()
                    .option("header", "true")
                    .option("compression", "snappy")
                    .option("encoding", usedEncoding)
                    .option("charset", usedEncoding) // 显式指定字符集
                    .option("maxRecordsPerFile", 90000000) // 设置每个文件最大记录数为9000万
                    .mode(SaveMode.Overwrite)//覆盖写
                    .parquet(outputPath);

            // 写入数据库
            writeToDatabase(df, sourceType, cleanStats);

            logger.info("转换逻辑执行完成，数据源类型: {}, 文件格式: {}", sourceType, fileFormat);
            df.unpersist();
            } catch (Exception e) {
            logger.error("转换崩了: {}", e.getMessage(), e);
            throw e;
        }
    }
    /**
     * 处理数据的通用逻辑
     * @param df 输入数据集 (now includes __source_file_path__ and __monotonic_id__)
     * @param sourceType 数据源类型
     * @param columnMappings 列映射 from JSON
     * @return 处理后的数据集
     */
    protected Dataset<Row> processData(Dataset<Row> df, String sourceType, Map<String, String> columnMappings) {
        logger.info("执行通用的数据处理逻辑");

        // Define UDF to extract filename from path
        UserDefinedFunction extractFilenameUdf = functions.udf(
            (String path) -> {
                if (path == null || path.isEmpty()) {
                    return "未知文件";
                }
                try {
                    return new File(path).getName();
                } catch (Exception e) {
                    logger.warn("从路径提取文件名失败: {}", path);
                    return path; // 至少保留原始路径作为文件名
                }
            }, DataTypes.StringType
        );

        spark.udf().register("extractFilename", extractFilenameUdf);

        // --- Generate 文件名 and 文件路径 columns ---
        if (!Arrays.asList(df.columns()).contains("__source_file_path__")) {
            logger.error("内部错误：__source_file_path__ 列未在 DataFrame 中找到！");
            // 添加一个默认的文件路径列，而不是简单地设为"未知文件"
            df = df.withColumn("__source_file_path__", functions.input_file_name());
            // 如果input_file_name()返回空，则使用自定义逻辑
            df = df.withColumn("__source_file_path__",
                functions.when(functions.col("__source_file_path__").isNull().or(functions.col("__source_file_path__").equalTo("")),
                    functions.lit(sourceType + "_数据"))
                .otherwise(functions.col("__source_file_path__")));
        }
        if (!Arrays.asList(df.columns()).contains("__monotonic_id__")) {
            logger.error("内部错误：__monotonic_id__ 列未在 DataFrame 中找到！");
           df = df.withColumn("__monotonic_id__", functions.monotonically_increasing_id());
           // Re-create window spec just in case
           WindowSpec windowSpec = Window.partitionBy("__source_file_path__").orderBy("__monotonic_id__");
           df = df.withColumn("__row_num__", functions.row_number().over(windowSpec)); // Add row number
        } else {
           // Generate row number if __monotonic_id__ exists
           WindowSpec windowSpec = Window.partitionBy("__source_file_path__").orderBy("__monotonic_id__");
           df = df.withColumn("__row_num__", functions.row_number().over(windowSpec)); // Add row number based on monotonic id
        }


        df = df.withColumn("文件名", functions.callUDF("extractFilename", functions.col("__source_file_path__")));
        df = df.withColumn("文件路径", functions.concat(
                functions.col("文件名"),
                functions.lit(":第"),
                functions.col("__row_num__").cast("string"),
                functions.lit("行")
        ));

        // --- Process mappings from JSON ---
        List<Column> columns = new ArrayList<>();
        Set<String> processedColumns = new HashSet<>();
        // Add required calculated columns first, ensuring they are included if mapped
         if (columnMappings.containsKey("文件名")) {
             columns.add(functions.col("文件名").as("文件名"));
             processedColumns.add("文件名");
         }
         if (columnMappings.containsKey("文件路径")) {
             columns.add(functions.col("文件路径").as("文件路径"));
             processedColumns.add("文件路径");
         }


        for (Map.Entry<String, String> entry : columnMappings.entrySet()) {
            String targetCol = entry.getKey();
            String sourceExpr = entry.getValue();

            // Skip already processed columns (文件名, 文件路径) and the placeholder mappings for them
             if (processedColumns.contains(targetCol) || targetCol.equals("文件名") || targetCol.equals("文件路径")) {
                continue;
            }

             // Skip the old '原始数据信息位置' if it accidentally exists in mappings
             if (targetCol.equals("原始数据信息位置")) {
                 logger.warn("发现旧的 '原始数据信息位置' 映射，将忽略。请使用 '文件路径'。");
                 continue;
             }

            try {
                 // Special handling for '收付标志' if already normalized (existing logic)
                 if (targetCol.equals("收付标志")) {
                     boolean useExistingNormalized = false;
                     boolean hasPaymentFlag = Arrays.asList(df.columns()).contains("收付标志");
                     if (hasPaymentFlag) {
                         try {
                             List<Row> sampleRows = df.select("收付标志").distinct().limit(10).collectAsList();
                             int totalValues = sampleRows.size();
                             int normalizedCount = 0;
                             for (Row row : sampleRows) {
                                 if (row.get(0) != null) {
                                     String value = row.get(0).toString().trim();
                                     if (value.equals("进") || value.equals("出")) {
                                         normalizedCount++;
                                     }
                                 }
                             }
                             if (totalValues > 0 && (double)normalizedCount / totalValues > 0.8) {
                                 useExistingNormalized = true;
                             }
                         } catch (Exception e) {
                             logger.warn("检查收付标志标准化状态时出错: {}", e.getMessage());
                         }
                     }
                     if (useExistingNormalized) {
                         logger.info("使用已标准化的收付标志列");
                         columns.add(functions.col("收付标志").as("收付标志"));
                         processedColumns.add(targetCol);
                         continue; // Skip further processing for this column
                     }
                 }

                // Build expression using utility function for other columns
                columns.add(TransformerUtils.buildExpressionColumn(df, sourceExpr, targetCol, logger));
                processedColumns.add(targetCol);
            } catch (Exception e) {
                logger.error("处理列表达式失败: {} -> {}, 错误: {}",
                        targetCol, sourceExpr, e.getMessage());
                // Add empty column on error to avoid schema issues downstream
                columns.add(functions.lit("").cast("string").as(targetCol));
                processedColumns.add(targetCol);
            }
        }

         // Ensure all target columns defined in the mapping exist in the final selection, even if processing failed or mapping was empty
         for (String targetColFromJson : columnMappings.keySet()) {
             if (!processedColumns.contains(targetColFromJson) && !targetColFromJson.equals("原始数据信息位置")) {
                 logger.warn("目标列 '{}' 在映射中定义但未成功处理，将添加为空列。", targetColFromJson);
                 columns.add(functions.lit("").cast("string").as(targetColFromJson));
             }
         }


        // Select ONLY the columns defined in the final list
        return df.select(columns.toArray(new Column[0]));
         // Note: The helper columns __source_file_path__, __monotonic_id__, __row_num__ are implicitly dropped here
         // because they are not included in the 'columns' list unless explicitly mapped.
    }

    /**
     * 数据前处理，子类可以重写此方法添加特定逻辑
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @return 处理后的数据集
     */
    protected Dataset<Row> preProcess(Dataset<Row> df, String sourceType) {
        // 调用通用清洗方法，删除收付标志为空的行
        return cleanEmptyPaymentFlag(df, sourceType);
    }

    /**
     * 数据后处理，子类可以重写此方法添加特定逻辑
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @return 处理后的数据集
     */
    protected Dataset<Row> postProcess(Dataset<Row> df, String sourceType) {
        // 默认实现：添加数据源类型列
        df = df.withColumn("交易类型", functions.lit(sourceType));

        // 处理对手户名为空的情况，将其替换为"未知"
        if (Arrays.asList(df.columns()).contains("对手户名")) {
            logger.info("检查并处理空的对手户名，将其替换为'未知'");
            df = df.withColumn("对手户名",
                functions.when(
                    functions.col("对手户名").isNull()
                    .or(functions.trim(functions.col("对手户名")).equalTo(""))
                    .or(functions.length(functions.trim(functions.col("对手户名"))).equalTo(0)),
                    functions.lit("未知")
                ).otherwise(functions.col("对手户名"))
            );
        }

        // 已在前处理中执行收付标志清洗
        return df;
    }

    /**
     * 清洗数据，删除收付标志为空的行
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @return 清洗后的数据集
     */
    protected Dataset<Row> cleanEmptyPaymentFlag(Dataset<Row> df, String sourceType) {
        // 可能的收付标志列名
        String[] possibleColumnNames = {
            "收付标志", "收/付标志", "收付类型", "收/付类型", "收支标志", "收/支标志",
            "收付", "收/付", "收支", "收/支", "付款标志", "收款标志", "借贷标志", "借/贷标志",
            "交易类别", "进出账标志", "借贷方向", "借贷", "收入支出", "收/支", "支出类型", "交易借贷",
            "交易方向", "资金方向", "账务标志"
        };

        String paymentFlagColumn = null;
        for (String col : df.columns()) {
            for (String possibleName : possibleColumnNames) {
                if (col.contains(possibleName)) {
                    paymentFlagColumn = col;
                    logger.info("找到收付标志列: {}", col);
                    break;
                }
            }
            if (paymentFlagColumn != null) break;
        }
        // 如果找不到收付标志列，返回原始数据集
        if (paymentFlagColumn == null) {
            logger.warn("未找到收付标志列，跳过清洗操作");
            return df;
        }
        // 检查列是否已经被标准化（如果列值只包含"进"和"出"，则认为已标准化）
        boolean alreadyNormalized = false;
        try {
            // 取样本行检查
            List<Row> sampleRows = df.select(paymentFlagColumn).distinct().limit(10).collectAsList();
            int totalValues = sampleRows.size();
            int normalizedCount = 0;
            for (Row row : sampleRows) {
                if (row.get(0) != null) {
                    String value = row.get(0).toString().trim();
                    if (value.equals("进") || value.equals("出")) {
                        normalizedCount++;
                    }
                }
            }
            if (totalValues > 0 && (double)normalizedCount / totalValues > 0.8) {
                alreadyNormalized = true;
                logger.info("收付标志列已标准化，跳过标准化步骤");
            }
        } catch (Exception e) {
            logger.warn("检查列标准化状态时出错: {}", e.getMessage());
        }
        // 生成临时列名，确保不与现有列冲突
        String normalizedCol = paymentFlagColumn;
        if (!alreadyNormalized) {
            normalizedCol = "temp_" + paymentFlagColumn + "_normalized";
            spark.udf().register("normalizeIncomeExpense",
                (String value) -> org.example.xiaowudemo.transformData.TransformFunction.normalizeIncomeExpense(value),
                DataTypes.StringType);
            df = df.withColumn(normalizedCol, functions.callUDF("normalizeIncomeExpense", functions.col(paymentFlagColumn)));
        }
        long beforeCount = df.count();
        logger.info("清洗前数据行数: {}", beforeCount);
        // 创建过滤条件：收付标志不为空、null或空字符串
        Column filterCondition = functions.col(normalizedCol).isNotNull()
                               .and(functions.trim(functions.col(normalizedCol)).notEqual(""))
                               .and(functions.length(functions.trim(functions.col(normalizedCol))).gt(0));
        Dataset<Row> emptyRows = df.filter(functions.not(filterCondition));
        long emptyCount = emptyRows.count();
        // 只有在发现空行时才记录详细信息
        if (emptyCount > 0) {
            logger.info("发现 {} 行收付标志为空的数据", emptyCount);
            // 保存被删除的行到日志目录（仅在数量大于0时）
            String logDir = "clean_logs";
            String logFilePath = logDir + "/" + sourceType + "_empty_payment_flag_rows.csv";
            try {
                // 保存被删除的行
                emptyRows.coalesce(1)
                    .write()
                    .option("header", "true")
                    .option("encoding", "UTF-8")
                    .mode(SaveMode.Overwrite)
                    .csv(logFilePath);

                logger.info("已将被删除的行保存到: {}", logFilePath);
            } catch (Exception e) {
                logger.error("保存被删除行时出错: {}", e.getMessage());
            }
        }
        // 过滤掉收付标志为空的行
        Dataset<Row> filteredDf = df.filter(filterCondition);
        // 如果使用了临时列，替换原始列并删除临时列
        if (!alreadyNormalized) {
            filteredDf = filteredDf.withColumn(paymentFlagColumn, functions.col(normalizedCol))
                                 .drop(normalizedCol);
        }
        // 统计清洗后的行数
        long afterCount = filteredDf.count();
        long removedCount = beforeCount - afterCount;
        // 只有在删除行时才记录详细信息
        if (removedCount > 0) {
            logger.info("清洗后数据行数: {}", afterCount);
            logger.info("删除了 {} 行收付标志为空的数据", removedCount);
            // 如果删除的行数超过总行数的一定比例，发出警告
            double deleteRatio = (double)removedCount / beforeCount;
            if (deleteRatio > 0.1) {  // 如果删除超过10%的行
                logger.warn("警告：删除了超过 {}% 的数据行，可能需要检查数据质量", String.format("%.2f", deleteRatio * 100));
            }
        }
        // 更新统计信息以供logCleaningStats使用
        spark.conf().set("clean_stats." + sourceType + ".empty_payment_flag_rows", removedCount);
        return filteredDf;
    }
    /**
     * 记录数据清洗统计信息
     * @param sourceType 数据源类型
     * @param fileFormat 文件格式
     * @param stats 统计信息
     */
    private void logCleaningStats(String sourceType, String fileFormat, Map<String, Long> stats) {
        // 从Spark配置中获取收付标志清洗统计
        long emptyPaymentFlagRowsRemoved = 0;
        try {
            String emptyPaymentFlagRowsKey = "clean_stats." + sourceType + ".empty_payment_flag_rows";
            if (spark.conf().contains(emptyPaymentFlagRowsKey)) {
                emptyPaymentFlagRowsRemoved = Long.parseLong(spark.conf().get(emptyPaymentFlagRowsKey));
            }
        } catch (Exception e) {
            logger.warn("获取清洗统计信息失败: {}", e.getMessage());
        }

        logger.info("======= 数据清洗统计信息: {} ({}) =======", sourceType, fileFormat);
        logger.info("原始行数: {}", stats.getOrDefault("原始行数", 0L));
        logger.info("移除的收付标志为空行数: {}", emptyPaymentFlagRowsRemoved);
        logger.info("最终保留行数: {}", stats.getOrDefault("最终保留行数", 0L));
        logger.info("删除率: {}%",
            String.format("%.2f",
                (1 - (double)stats.getOrDefault("最终保留行数", 0L) /
                      stats.getOrDefault("原始行数", 1L)) * 100));

        // 将清洗统计信息保存到文件
        String logDir = "clean_logs";
        String statsFilePath = logDir + "/" + sourceType + "_" + fileFormat + "_cleaning_stats.txt";

        try (PrintWriter writer = new PrintWriter(new FileWriter(statsFilePath, true))) {
            writer.println("======= 数据清洗统计信息: " + new java.util.Date() + " =======");
            writer.println("数据源类型: " + sourceType);
            writer.println("文件格式: " + fileFormat);
            writer.println("原始行数: " + stats.getOrDefault("原始行数", 0L));
            writer.println("移除的收付标志为空行数: " + emptyPaymentFlagRowsRemoved);
            writer.println("最终保留行数: " + stats.getOrDefault("最终保留行数", 0L));
            writer.println("删除率: " +
                String.format("%.2f",
                    (1 - (double)stats.getOrDefault("最终保留行数", 0L) /
                          stats.getOrDefault("原始行数", 1L)) * 100) + "%");
            writer.println("=======================================");

            logger.info("清洗统计信息已保存到: {}", statsFilePath);
        } catch (Exception e) {
            logger.error("保存清洗统计信息时出错: {}", e.getMessage());
        }
    }

    /**
     * 将清洗后的数据写入数据库
     * @param df 清洗后的数据框
     * @param sourceType 数据源类型
     * @param cleanStats 清洗统计信息
     */
    protected void writeToDatabase(Dataset<Row> df, String sourceType, Map<String, Long> cleanStats) {
        if (databaseService == null) {
            logger.warn("数据库服务未配置，跳过数据库写入操作");
            return;
        }

        logger.info("=== 开始优化的数据库写入流程，数据源: {} ===", sourceType);
        long startTime = System.currentTimeMillis();

        try {
            // 1. 检查数据库连接
            if (!databaseService.checkDatabaseConnection()) {
                logger.error("数据库连接检查失败，跳过写入操作");
                return;
            }

            // 确保所有列非空，将 null 替换为 ""
            String[] allColumns = df.columns();
            for (String col : allColumns) {
                df = df.withColumn(col, functions.coalesce(functions.col(col), functions.lit("")));
            }
            logger.info("已将所有列的 null 值替换为空字符串");

            // 3. 根据数据源类型判断数据类型并分别写入
            String dataSourceLower = sourceType.toLowerCase();
            long totalRecords = 0;
            
            // 3.1 如果是账户相关数据，写入账户信息表
            if (dataSourceLower.contains("账户") || dataSourceLower.contains("account")) {
                logger.info("识别为账户信息数据，写入账户信息表");
                long accountRecords = databaseService.saveAccountData(df, sourceType);
                totalRecords += accountRecords;
                logger.info("账户信息写入完成: {} 条", accountRecords);
                cleanStats.put("账户信息记录数", accountRecords);
            }
            // 3.2 如果是人员相关数据，写入人员信息表
            else if (dataSourceLower.contains("人员") || dataSourceLower.contains("客户") || 
                     dataSourceLower.contains("personnel") || dataSourceLower.contains("customer")) {
                logger.info("识别为人员信息数据，写入人员信息表");
                long personnelRecords = databaseService.savePersonnelData(df, sourceType);
                totalRecords += personnelRecords;
                logger.info("人员信息写入完成: {} 条", personnelRecords);
                cleanStats.put("人员信息记录数", personnelRecords);
            }
            // 3.3 如果是任务相关数据，写入任务信息表
            else if (dataSourceLower.contains("任务") || dataSourceLower.contains("task")) {
                logger.info("识别为任务信息数据，写入任务信息表");
                long taskRecords = databaseService.saveTaskData(df, sourceType);
                totalRecords += taskRecords;
                logger.info("任务信息写入完成: {} 条", taskRecords);
                cleanStats.put("任务信息记录数", taskRecords);
            }
            // 3.4 默认情况：交易流水数据
            else {
                logger.info("识别为交易流水数据，写入交易流水表");
                long transactionRecords = databaseService.saveTransactionData(df, sourceType);
                totalRecords += transactionRecords;
                logger.info("交易流水写入完成: {} 条", transactionRecords);
                cleanStats.put("交易流水记录数", transactionRecords);
            }

            // 4. 执行联表更新（关键步骤）
            logger.info("=== 执行联表更新操作 ===");
            try {
                int updatedRecords = databaseService.performJoinTableUpdate();
                logger.info("联表更新完成: {} 条记录的证件号码已补充", updatedRecords);
                cleanStats.put("联表更新记录数", (long)updatedRecords);
            } catch (Exception e) {
                logger.warn("联表更新操作失败，但不影响数据写入: {}", e.getMessage());
                cleanStats.put("联表更新记录数", 0L);
            }

            // 5. 统计和日志
            long endTime = System.currentTimeMillis();
            logger.info("=== 数据库写入流程完成 ===");
            logger.info("数据源: {}", sourceType);
            logger.info("总写入记录数: {}", totalRecords);
            logger.info("联表更新记录数: {}", cleanStats.getOrDefault("联表更新记录数", 0L));
            logger.info("总耗时: {} ms", endTime - startTime);

            // 6. 更新统计信息
            cleanStats.put("数据库写入记录数", totalRecords);
            cleanStats.put("数据库写入耗时", endTime - startTime);

            // 7. 打印数据库统计（每5次处理显示一次，避免日志过多）
            if (Math.random() < 0.2) { // 20%的概率显示统计信息
                try {
                    String dbStats = databaseService.getDatabaseStats();
                    logger.info("数据库统计信息:\n{}", dbStats);
                } catch (Exception e) {
                    logger.debug("获取数据库统计信息失败: {}", e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("数据库写入失败，数据源: {}, 错误: {}", sourceType, e.getMessage(), e);
            cleanStats.put("数据库写入错误", 1L);
            // 不抛出异常，避免影响整个处理流程
        }
    }

    /**
     * 根据数据源类型确定对应的数据库表类型
     * @param sourceType 数据源类型
     * @return 表类型
     */
    protected String determineTableType(String sourceType) {
        if (sourceType == null) {
            return "transaction"; // 默认为交易流水表
        }

        String lowerSourceType = sourceType.toLowerCase();
        
        // 根据数据源类型映射到表类型
        if (lowerSourceType.contains("交易流水") || lowerSourceType.contains("支付宝") || 
            lowerSourceType.contains("微信") || lowerSourceType.contains("银行") && lowerSourceType.contains("交易")) {
            return "transaction";
        } else if (lowerSourceType.contains("人员信息") || lowerSourceType.contains("客户信息")) {
            return "personnel";
        } else if (lowerSourceType.contains("任务信息") || lowerSourceType.contains("task")) {
            return "task";
        } else if (lowerSourceType.contains("账户信息") || lowerSourceType.contains("account")) {
            return "account";
        } else {
            // 默认情况下，根据文件内容推断
            return "transaction"; // 大多数情况下是交易数据
        }
    }

    /**
     * 将DataFrame列名映射为数据库表字段名
     * @param df 原始DataFrame
     * @param tableType 表类型
     * @return 映射后的DataFrame
     */
    protected Dataset<Row> mapColumnsForDatabase(Dataset<Row> df, String tableType) {
        logger.info("开始映射DataFrame列名到数据库字段，表类型: {}", tableType);
        
        try {
            // 获取当前列名
            String[] currentColumns = df.columns();
            logger.debug("当前DataFrame列名: {}", String.join(", ", currentColumns));
            
            // 根据表类型进行列名映射
            Dataset<Row> mappedDf = df;
            
            switch (tableType.toLowerCase()) {
                case "transaction":
                    mappedDf = mapTransactionColumns(df);
                    break;
                case "personnel":
                    mappedDf = mapPersonnelColumns(df);
                    break;
                case "task":
                    mappedDf = mapTaskColumns(df);
                    break;
                case "account":
                    mappedDf = mapAccountColumns(df);
                    break;
                default:
                    logger.warn("未知表类型: {}, 使用原始列名", tableType);
            }
            
            logger.debug("映射后DataFrame列名: {}", String.join(", ", mappedDf.columns()));
            return mappedDf;
            
        } catch (Exception e) {
            logger.error("列名映射失败: {}", e.getMessage(), e);
            return df; // 返回原始DataFrame
        }
    }

    /**
     * 映射交易流水表的列名
     */
    protected Dataset<Row> mapTransactionColumns(Dataset<Row> df) {
        // 创建列名映射表（中文列名 -> 数据库字段名）
        Map<String, String> columnMapping = new HashMap<>();
        columnMapping.put("交易卡号", "transaction_card_number");
        columnMapping.put("交易账号", "transaction_account");
        columnMapping.put("交易方户名", "transaction_holder_name");
        columnMapping.put("交易方证件号码", "transaction_holder_id");
        columnMapping.put("交易时间", "transaction_time");
        columnMapping.put("交易金额", "transaction_amount");
        columnMapping.put("交易余额", "transaction_balance");
        columnMapping.put("收付标志", "payment_flag");
        columnMapping.put("交易对手账卡号", "counterpart_account");
        columnMapping.put("现金标志", "cash_flag");
        columnMapping.put("对手户名", "counterpart_name");
        columnMapping.put("对手身份证号", "counterpart_id");
        columnMapping.put("对手开户银行", "counterpart_bank");
        columnMapping.put("摘要说明", "summary_description");
        columnMapping.put("交易币种", "transaction_currency");
        columnMapping.put("交易网点名称", "transaction_branch");
        columnMapping.put("交易发生地", "transaction_location");
        columnMapping.put("交易是否成功", "transaction_success");
        columnMapping.put("传票号", "voucher_number");
        columnMapping.put("IP地址", "ip_address");
        columnMapping.put("MAC地址", "mac_address");
        columnMapping.put("对手交易余额", "counterpart_balance");
        columnMapping.put("交易流水号", "transaction_serial_number");
        columnMapping.put("日志号", "log_number");
        columnMapping.put("凭证种类", "voucher_type");
        columnMapping.put("凭证号", "voucher_id");
        columnMapping.put("交易柜员号", "teller_number");
        columnMapping.put("备注", "remarks");
        columnMapping.put("商户名称", "merchant_name");
        columnMapping.put("交易类型", "transaction_type");
        columnMapping.put("查询反馈结果原因", "query_feedback_result");
        columnMapping.put("开户行", "opening_bank");
        columnMapping.put("文件路径", "file_path");
        columnMapping.put("文件名", "file_name");
        columnMapping.put("data_source", "data_source");

        return applyColumnMapping(df, columnMapping);
    }

    /**
     * 映射人员信息表的列名
     */
    protected Dataset<Row> mapPersonnelColumns(Dataset<Row> df) {
        Map<String, String> columnMapping = new HashMap<>();
        columnMapping.put("客户名称", "customer_name");
        columnMapping.put("证照类型", "certificate_type");
        columnMapping.put("证照号码", "certificate_number");
        columnMapping.put("单位地址", "company_address");
        columnMapping.put("联系电话", "contact_phone");
        columnMapping.put("联系手机", "mobile_phone");
        columnMapping.put("单位电话", "company_phone");
        columnMapping.put("住宅电话", "home_phone");
        columnMapping.put("工作单位", "work_unit");
        columnMapping.put("邮箱地址", "email_address");
        columnMapping.put("代办人姓名", "agent_name");
        columnMapping.put("代办人证件类型", "agent_certificate_type");
        columnMapping.put("代办人证件号码", "agent_certificate_number");
        columnMapping.put("国税纳税号", "national_tax_number");
        columnMapping.put("地税纳税号", "local_tax_number");
        columnMapping.put("法人代表", "legal_representative");
        columnMapping.put("法人代表证件类型", "legal_rep_certificate_type");
        columnMapping.put("法人代表证件号码", "legal_rep_certificate_number");
        columnMapping.put("出生日期", "birth_date");
        columnMapping.put("户籍地址", "registered_address");
        columnMapping.put("客户工商执照号码", "business_license_number");
        columnMapping.put("文件名", "file_name");
        columnMapping.put("文件路径", "file_path");
        columnMapping.put("data_source", "data_source");

        return applyColumnMapping(df, columnMapping);
    }

    /**
     * 映射任务信息表的列名
     */
    protected Dataset<Row> mapTaskColumns(Dataset<Row> df) {
        Map<String, String> columnMapping = new HashMap<>();
        columnMapping.put("任务流水号", "task_serial_number");
        columnMapping.put("银行名称", "bank_name");
        columnMapping.put("主体类别", "entity_category");
        columnMapping.put("证帐号码", "account_certificate_number");
        columnMapping.put("发送时间", "send_time");
        columnMapping.put("反馈时间", "feedback_time");
        columnMapping.put("反馈结果", "feedback_result");
        columnMapping.put("反馈非明细结果", "feedback_non_detail_result");
        columnMapping.put("反馈明细结果", "feedback_detail_result");
        columnMapping.put("入库时间", "storage_time");
        columnMapping.put("入库状态", "storage_status");
        columnMapping.put("请求单号", "request_number");
        columnMapping.put("查询结果", "query_result");
        columnMapping.put("文件名", "file_name");
        columnMapping.put("文件路径", "file_path");
        columnMapping.put("data_source", "data_source");

        return applyColumnMapping(df, columnMapping);
    }

    /**
     * 映射账户信息表的列名
     */
    protected Dataset<Row> mapAccountColumns(Dataset<Row> df) {
        Map<String, String> columnMapping = new HashMap<>();
        columnMapping.put("账户开户名称", "account_holder_name");
        columnMapping.put("开户人证件号码", "holder_certificate_number");
        columnMapping.put("交易卡号", "transaction_card_number");
        columnMapping.put("交易账号", "transaction_account");
        columnMapping.put("账号开户时间", "account_opening_time");
        columnMapping.put("账户余额", "account_balance");
        columnMapping.put("可用余额", "available_balance");
        columnMapping.put("币种", "currency");
        columnMapping.put("开户网点代码", "opening_branch_code");
        columnMapping.put("开户网点", "opening_branch");
        columnMapping.put("账户状态", "account_status");
        columnMapping.put("钞汇标志名称", "cash_exchange_flag");
        columnMapping.put("开户人证件类型", "holder_certificate_type");
        columnMapping.put("销户日期", "account_closing_date");
        columnMapping.put("账户类型", "account_type");
        columnMapping.put("开户联系方式", "opening_contact");
        columnMapping.put("通信地址", "communication_address");
        columnMapping.put("联系电话", "contact_phone");
        columnMapping.put("代理人", "agent");
        columnMapping.put("代理人电话", "agent_phone");
        columnMapping.put("备注", "remarks");
        columnMapping.put("开户省份", "opening_province");
        columnMapping.put("开户城市", "opening_city");
        columnMapping.put("账号开户银行", "account_opening_bank");
        columnMapping.put("客户代码", "customer_code");
        columnMapping.put("法人代表", "legal_representative");
        columnMapping.put("客户工商执照号码", "business_license_number");
        columnMapping.put("法人代表证件号码", "legal_rep_certificate_number");
        columnMapping.put("住宅地址", "residential_address");
        columnMapping.put("邮政编码", "postal_code");
        columnMapping.put("代办人证件号码", "agent_certificate_number");
        columnMapping.put("邮箱地址", "email_address");
        columnMapping.put("关联资金账户", "related_fund_account");
        columnMapping.put("地税纳税号", "local_tax_number");
        columnMapping.put("单位电话", "company_phone");
        columnMapping.put("代办人证件类型", "agent_certificate_type");
        columnMapping.put("住宅电话", "home_phone");
        columnMapping.put("法人代表证件类型", "legal_rep_certificate_type");
        columnMapping.put("国税纳税号", "national_tax_number");
        columnMapping.put("单位地址", "company_address");
        columnMapping.put("工作单位", "work_unit");
        columnMapping.put("销户网点", "closing_branch");
        columnMapping.put("最后交易时间", "last_transaction_time");
        columnMapping.put("账户销户银行", "account_closing_bank");
        columnMapping.put("任务流水号", "task_serial_number");
        columnMapping.put("文件名", "file_name");
        columnMapping.put("文件路径", "file_path");
        columnMapping.put("data_source", "data_source");

        return applyColumnMapping(df, columnMapping);
    }

    /**
     * 应用列名映射
     */
    protected Dataset<Row> applyColumnMapping(Dataset<Row> df, Map<String, String> columnMapping) {
        Dataset<Row> result = df;
        
        for (Map.Entry<String, String> entry : columnMapping.entrySet()) {
            String oldName = entry.getKey();
            String newName = entry.getValue();
            
            // 检查列是否存在，如果存在则重命名
            if (Arrays.asList(result.columns()).contains(oldName)) {
                result = result.withColumnRenamed(oldName, newName);
                logger.debug("列名映射: {} -> {}", oldName, newName);
            }
        }
        
        return result;
    }
}