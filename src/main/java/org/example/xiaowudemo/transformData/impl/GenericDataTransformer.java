package org.example.xiaowudemo.transformData.impl;

import org.apache.spark.sql.*;
import org.apache.spark.sql.expressions.UserDefinedFunction;
import org.apache.spark.sql.types.DataTypes;
import org.example.xiaowudemo.transformData.AbstractDataTransformer;
import org.example.xiaowudemo.transformData.TransformFunction;
import org.example.xiaowudemo.service.DatabaseService;
import static org.apache.spark.sql.functions.udf;

import java.util.*;

/**
 * 通用数据转换器实现类，适用于大多数数据源
 */
public class GenericDataTransformer extends AbstractDataTransformer {

    /**
     * 构造函数
     * @param spark Spark会话
     * @param classifiedDir 分类目录
     * @param databaseService 数据库服务
     */
    public GenericDataTransformer(SparkSession spark, String classifiedDir, DatabaseService databaseService) {
        super(spark, classifiedDir, databaseService);
    }

    /**
     * 构造函数（兼容旧版本）
     * @param spark Spark会话
     * @param classifiedDir 分类目录
     */
    @Deprecated
    public GenericDataTransformer(SparkSession spark, String classifiedDir) {
        this(spark, classifiedDir, null);
    }

    /**
     * 注册通用特有的UDF函数
     */
    @Override
    protected void registerSourceSpecificUDFs() {
        logger.info("注册通用特有的UDF函数");
        
        // 注册normalizeCardNumber UDF函数
        UserDefinedFunction normalizeCardNumberUDF = udf(
            (String cardNumber) -> TransformFunction.normalizeCardNumber(cardNumber),
            DataTypes.StringType
        );
        spark.udf().register("normalize_card_number", normalizeCardNumberUDF);
        logger.info("已注册normalize_card_number UDF函数");
        
        // 注册其他通用UDF函数
        UserDefinedFunction normalizeIncomeExpenseUDF = udf(
            (String value) -> TransformFunction.normalizeIncomeExpense(value),
            DataTypes.StringType
        );
        spark.udf().register("normalize_income_expense", normalizeIncomeExpenseUDF);
        
        UserDefinedFunction normalizeDateUDF = udf(
            (String dateStr) -> TransformFunction.normalizeDate(dateStr),
            DataTypes.StringType
        );
        spark.udf().register("normalize_date", normalizeDateUDF);
    }
    /**
     * 通用数据前处理
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> preProcess(Dataset<Row> df, String sourceType) {
        logger.info("执行通用数据特有的前处理逻辑");
        
        // 先调用父类的前处理方法，包括清洗收付标志为空的行
        df = super.preProcess(df, sourceType);
        
        // 通用数据特有的前处理逻辑
        Set<String> renamedColumns = new HashSet<>();
        Set<String> existingTargetColumns = new HashSet<>();
        for (String col : df.columns()) {
            existingTargetColumns.add(col);
        }
        for (String col : df.columns()) {
            if (renamedColumns.contains(col)) {
                continue;
            }
            if ((col.contains("金额") || col.contains("金") || col.contains("元")) && !existingTargetColumns.contains("交易金额")) {
                df = df.withColumnRenamed(col, "交易金额");
                logger.info("重命名列: '{}' -> '交易金额'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易金额");
            }
            else if ((col.contains("时间") || col.contains("日期")) && !existingTargetColumns.contains("交易时间")) {
                df = df.withColumnRenamed(col, "交易时间");
                logger.info("重命名列: '{}' -> '交易时间'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易时间");
            }
            else if ((col.contains("账号") || col.contains("账户") || col.contains("卡号")) && !existingTargetColumns.contains("账号")) {
                df = df.withColumnRenamed(col, "账号");
                logger.info("重命名列: '{}' -> '账号'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("账号");
            }
            else if ((col.contains("姓名") || col.contains("户名") || col.contains("名称")) && !existingTargetColumns.contains("姓名")) {
                df = df.withColumnRenamed(col, "姓名");
                logger.info("重命名列: '{}' -> '姓名'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("姓名");
            }
        }
        
        return df;
    }
    
    /**
     * 处理通用数据的特定业务逻辑
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @param columnMappings 列映射
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> processData(Dataset<Row> df, String sourceType, Map<String, String> columnMappings) {
        logger.info("执行通用数据处理逻辑");
        // 调用父类的通用处理逻辑
        return super.processData(df, sourceType, columnMappings);
    }
    /**
     * 通用数据后处理
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> postProcess(Dataset<Row> df, String sourceType) {
        // 调用父类的通用处理逻辑
        return super.postProcess(df, sourceType);
    }
} 