package org.example.xiaowudemo.transformData.impl;

import org.apache.spark.sql.*;
import org.apache.spark.sql.expressions.UserDefinedFunction;
import org.apache.spark.sql.types.DataTypes;
import org.example.xiaowudemo.transformData.AbstractDataTransformer;
import org.example.xiaowudemo.transformData.TransformFunction;
import org.example.xiaowudemo.service.DatabaseService;
import static org.apache.spark.sql.functions.udf;

import java.util.*;

/**
 * 支付宝数据转换器实现类
 */
public class AlipayDataTransformer extends AbstractDataTransformer {

    /**
     * 构造函数
     * @param spark Spark会话
     * @param classifiedDir 分类目录
     * @param databaseService 数据库服务
     */
    public AlipayDataTransformer(SparkSession spark, String classifiedDir, DatabaseService databaseService) {
        super(spark, classifiedDir, databaseService);
    }

    /**
     * 构造函数（兼容旧版本）
     * @param spark Spark会话
     * @param classifiedDir 分类目录
     */
    @Deprecated
    public AlipayDataTransformer(SparkSession spark, String classifiedDir) {
        this(spark, classifiedDir, null);
    }

    /**
     * 注册支付宝特有的UDF函数
     */
    @Override
    protected void registerSourceSpecificUDFs() {
        logger.info("注册支付宝特有的UDF函数");
        UserDefinedFunction normalizeCardNumberUDF = udf(
            (String cardNumber) -> TransformFunction.normalizeCardNumber(cardNumber),
            DataTypes.StringType
        );
        spark.udf().register("normalize_card_number", normalizeCardNumberUDF);
        logger.info("已注册normalize_card_number UDF函数");
        
        // 注册extractName UDF函数
        UserDefinedFunction extractNameUDF = udf(
            (String field) -> TransformFunction.extractName(field),
            DataTypes.StringType
        );
        spark.udf().register("extract_name", extractNameUDF);
        
        // 注册extractAccount UDF函数
        UserDefinedFunction extractAccountUDF = udf(
            (String field) -> TransformFunction.extractAccount(field),
            DataTypes.StringType
        );
        spark.udf().register("extract_account", extractAccountUDF);
        
        // 注册normalizeIncomeExpense UDF函数
        UserDefinedFunction normalizeIncomeExpenseUDF = udf(
            (String value) -> TransformFunction.normalizeIncomeExpense(value),
            DataTypes.StringType
        );
        spark.udf().register("normalize_income_expense", normalizeIncomeExpenseUDF);
        
        // 注册latest UDF函数
        UserDefinedFunction latestUDF = udf(
            (String time1, String time2, String time3) ->
                TransformFunction.latest(time1, time2, time3),
            DataTypes.StringType
        );
        spark.udf().register("latest", latestUDF);
    }
    
    /**
     * 支付宝数据前处理
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> preProcess(Dataset<Row> df, String sourceType) {
        logger.info("执行支付宝特有的前处理逻辑");
        
        // 先调用父类的前处理方法，包括清洗收付标志为空的行
        df = super.preProcess(df, sourceType);
        
        // 支付宝特有的前处理逻辑
        Set<String> renamedColumns = new HashSet<>();
        Set<String> existingTargetColumns = new HashSet<>();
        Collections.addAll(existingTargetColumns, df.columns());
        for (String col : df.columns()) {
            if (renamedColumns.contains(col)) {
                continue;
            }
            if (col.contains("金额") && col.contains("元") && !existingTargetColumns.contains("金额（元）")) {
                df = df.withColumnRenamed(col, "金额（元）");
                logger.info("重命名列: '{}' -> '金额（元）'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("金额（元）");
            }
            else if (col.contains("交易") && col.contains("号") && !existingTargetColumns.contains("交易号")) {
                df = df.withColumnRenamed(col, "交易号");
                logger.info("重命名列: '{}' -> '交易号'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易号");
            }
            else if (col.contains("收") && col.contains("支") && !existingTargetColumns.contains("收/支")) {
                df = df.withColumnRenamed(col, "收/支");
                logger.info("重命名列: '{}' -> '收/支'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("收/支");
            }
            else if (col.contains("交易") && col.contains("创建") && col.contains("时间") && !existingTargetColumns.contains("交易创建时间")) {
                df = df.withColumnRenamed(col, "交易创建时间");
                logger.info("重命名列: '{}' -> '交易创建时间'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易创建时间");
            }
            else if (col.contains("付款") && col.contains("时间") && !existingTargetColumns.contains("付款时间")) {
                df = df.withColumnRenamed(col, "付款时间");
                logger.info("重命名列: '{}' -> '付款时间'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("付款时间");
            }
            else if (col.contains("最近") && col.contains("修改") && col.contains("时间") && !existingTargetColumns.contains("最近修改时间")) {
                df = df.withColumnRenamed(col, "最近修改时间");
                logger.info("重命名列: '{}' -> '最近修改时间'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("最近修改时间");
            }
            else if (col.contains("用户") && col.contains("信息") && !existingTargetColumns.contains("用户信息")) {
                df = df.withColumnRenamed(col, "用户信息");
                logger.info("重命名列: '{}' -> '用户信息'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("用户信息");
            }
            else if (col.contains("消费") && col.contains("名称") && !existingTargetColumns.contains("消费名称")) {
                df = df.withColumnRenamed(col, "消费名称");
                logger.info("重命名列: '{}' -> '消费名称'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("消费名称");
            }
        }
        
        return df;
    }
    
    /**
     * 处理支付宝数据的特定业务逻辑
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @param columnMappings 列映射
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> processData(Dataset<Row> df, String sourceType, Map<String, String> columnMappings) {
        logger.info("执行支付宝特有的数据处理逻辑");
        // 调用父类的通用处理逻辑
        return super.processData(df, sourceType, columnMappings);
    }
    /**
     * 支付宝数据后处理
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> postProcess(Dataset<Row> df, String sourceType) {
        // 调用父类的通用处理逻辑
        return super.postProcess(df, sourceType);
    }
} 