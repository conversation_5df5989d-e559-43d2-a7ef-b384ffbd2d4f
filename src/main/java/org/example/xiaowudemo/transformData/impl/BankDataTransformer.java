package org.example.xiaowudemo.transformData.impl;

import org.apache.spark.sql.*;
import org.apache.spark.sql.expressions.UserDefinedFunction;
import org.apache.spark.sql.types.DataTypes;
import org.example.xiaowudemo.transformData.AbstractDataTransformer;
import org.example.xiaowudemo.transformData.TransformFunction;
import org.example.xiaowudemo.service.DatabaseService;
import static org.apache.spark.sql.functions.udf;

import java.util.*;

/**
 * 银行数据转换器实现类
 */
public class BankDataTransformer extends AbstractDataTransformer {

    /**
     * 构造函数
     * @param spark Spark会话
     * @param classifiedDir 分类目录
     * @param databaseService 数据库服务
     */
    public BankDataTransformer(SparkSession spark, String classifiedDir, DatabaseService databaseService) {
        super(spark, classifiedDir, databaseService);
    }

    /**
     * 构造函数（兼容旧版本）
     * @param spark Spark会话
     * @param classifiedDir 分类目录
     */
    @Deprecated
    public BankDataTransformer(SparkSession spark, String classifiedDir) {
        this(spark, classifiedDir, null);
    }

    /**
     * 注册银行特有的UDF函数
     */
    @Override
    protected void registerSourceSpecificUDFs() {
        logger.info("注册银行特有的UDF函数");
        
        // 注册normalizeCardNumber UDF函数
        UserDefinedFunction normalizeCardNumberUDF = udf(
            (String cardNumber) -> TransformFunction.normalizeCardNumber(cardNumber),
            DataTypes.StringType
        );
        spark.udf().register("normalize_card_number", normalizeCardNumberUDF);
        logger.info("已注册normalize_card_number UDF函数");
    }

    /**
     * 银行数据前处理
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> preProcess(Dataset<Row> df, String sourceType) {
        logger.info("执行银行特有的前处理逻辑");
        
        // 先调用父类的前处理方法，包括清洗收付标志为空的行
        df = super.preProcess(df, sourceType);
        
        // 银行特有的前处理逻辑
        Set<String> renamedColumns = new HashSet<>();
        Set<String> existingTargetColumns = new HashSet<>(Arrays.asList(df.columns()));
        for (String col : df.columns()) {
            if (renamedColumns.contains(col)) {
                continue;
            }
            if (col.contains("交易方") && col.contains("户名") && !existingTargetColumns.contains("交易方户名")) {
                df = df.withColumnRenamed(col, "交易方户名");
                logger.info("重命名列: '{}' -> '交易方户名'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易方户名");
            }
            else if (col.contains("交易") && col.contains("卡号") && !existingTargetColumns.contains("交易卡号")) {
                df = df.withColumnRenamed(col, "交易卡号");
                logger.info("重命名列: '{}' -> '交易卡号'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易卡号");
            }
            else if (col.contains("交易") && col.contains("网点") && !existingTargetColumns.contains("交易网点名称")) {
                df = df.withColumnRenamed(col, "交易网点名称");
                logger.info("重命名列: '{}' -> '交易网点名称'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易网点名称");
            }
            else if (col.contains("交易方") && col.contains("证件") && !existingTargetColumns.contains("交易方证件号码")) {
                df = df.withColumnRenamed(col, "交易方证件号码");
                logger.info("重命名列: '{}' -> '交易方证件号码'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易方证件号码");
            }
            else if ((col.contains("金额") || col.contains("金") || col.contains("元")) && 
                     !existingTargetColumns.contains("交易金额") && 
                     !col.equals("交易金额")) {
                df = df.withColumnRenamed(col, "交易金额");
                logger.info("重命名列: '{}' -> '交易金额'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易金额");
            }
            else if (col.contains("交易") && col.contains("时间") && !existingTargetColumns.contains("交易时间")) {
                df = df.withColumnRenamed(col, "交易时间");
                logger.info("重命名列: '{}' -> '交易时间'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易时间");
            }
        }
        return df;
    }
    
    /**
     * 处理银行数据的特定业务逻辑
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @param columnMappings 列映射
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> processData(Dataset<Row> df, String sourceType, Map<String, String> columnMappings) {
        logger.info("执行银行特有的数据处理逻辑");
        // 调用父类的通用处理逻辑
        return super.processData(df, sourceType, columnMappings);
    }

    /**
     * 银行数据后处理
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> postProcess(Dataset<Row> df, String sourceType) {
        // 调用父类的通用处理逻辑
        return super.postProcess(df, sourceType);
    }
} 