package org.example.xiaowudemo.transformData.impl;

import org.apache.spark.sql.*;
import org.apache.spark.sql.expressions.UserDefinedFunction;
import org.apache.spark.sql.types.DataTypes;
import org.example.xiaowudemo.transformData.AbstractDataTransformer;
import org.example.xiaowudemo.transformData.TransformFunction;
import org.example.xiaowudemo.service.DatabaseService;
import static org.apache.spark.sql.functions.udf;
import static org.apache.spark.sql.functions.col;

import java.util.*;
import java.util.Arrays;

/**
 * 微信数据转换器实现类
 */
public class WechatDataTransformer extends AbstractDataTransformer {

    /**
     * 构造函数
     * @param spark Spark会话
     * @param classifiedDir 分类目录
     * @param databaseService 数据库服务
     */
    public WechatDataTransformer(SparkSession spark, String classifiedDir, DatabaseService databaseService) {
        super(spark, classifiedDir, databaseService);
    }

    /**
     * 构造函数（兼容旧版本）
     * @param spark Spark会话
     * @param classifiedDir 分类目录
     */
    @Deprecated
    public WechatDataTransformer(SparkSession spark, String classifiedDir) {
        this(spark, classifiedDir, null);
    }

    /**
     * 注册微信特有的UDF函数
     */
    @Override
    protected void registerSourceSpecificUDFs() {
        logger.info("注册微信特有的UDF函数");
        
        // 注册normalizeCardNumber UDF函数
        UserDefinedFunction normalizeCardNumberUDF = udf(
            (String cardNumber) -> TransformFunction.normalizeCardNumber(cardNumber),
            DataTypes.StringType
        );
        spark.udf().register("normalize_card_number", normalizeCardNumberUDF);
        logger.info("已注册normalize_card_number UDF函数");
        
        // 注册normalizeIncomeExpense UDF函数
        UserDefinedFunction normalizeIncomeExpenseUDF = udf(
            (String value) -> TransformFunction.normalizeIncomeExpense(value),
            DataTypes.StringType
        );
        spark.udf().register("normalize_income_expense", normalizeIncomeExpenseUDF);
    }
    
    /**
     * 微信数据前处理
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> preProcess(Dataset<Row> df, String sourceType) {
        logger.info("执行微信特有的前处理逻辑");
        
        // 先调用父类的前处理方法，包括清洗收付标志为空的行
        df = super.preProcess(df, sourceType);
        
        // 微信特有的前处理逻辑
        Set<String> renamedColumns = new HashSet<>();
        Set<String> existingTargetColumns = new HashSet<>();
        for (String col : df.columns()) {
            existingTargetColumns.add(col);
        }
        for (String col : df.columns()) {
            if (renamedColumns.contains(col)) {
                continue;
            }
            if ((col.contains("金额") || col.contains("金") || col.contains("元") || col.contains("收入") || col.contains("支出")) && 
                !col.contains("余额") && !col.contains("贷款") && !existingTargetColumns.contains("交易金额")) {
                df = df.withColumnRenamed(col, "交易金额");
                logger.info("重命名列: '{}' -> '交易金额'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易金额");
            }
            else if ((col.contains("余额") || col.contains("结余")) && !existingTargetColumns.contains("账户余额")) {
                df = df.withColumnRenamed(col, "账户余额");
                logger.info("重命名列: '{}' -> '账户余额'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("账户余额");
            }
            else if (col.contains("贷款") && col.contains("类型") && !existingTargetColumns.contains("贷款类型")) {
                df = df.withColumnRenamed(col, "贷款类型");
                logger.info("重命名列: '{}' -> '贷款类型'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("贷款类型");
            }
            else if (col.contains("交易") && col.contains("号") && !existingTargetColumns.contains("交易号")) {
                df = df.withColumnRenamed(col, "交易号");
                logger.info("重命名列: '{}' -> '交易号'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易号");
            }
            else if (col.contains("交易") && col.contains("时间") && !existingTargetColumns.contains("交易时间")) {
                df = df.withColumnRenamed(col, "交易时间");
                logger.info("重命名列: '{}' -> '交易时间'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易时间");
            }
            else if (col.contains("用户") && col.contains("ID") && !existingTargetColumns.contains("用户ID")) {
                df = df.withColumnRenamed(col, "用户ID");
                logger.info("重命名列: '{}' -> '用户ID'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("用户ID");
            }
            else if (col.contains("用户") && col.contains("银行") && col.contains("卡") && !existingTargetColumns.contains("用户银行卡号")) {
                df = df.withColumnRenamed(col, "用户银行卡号");
                logger.info("重命名列: '{}' -> '用户银行卡号'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("用户银行卡号");
            }
            else if (col.contains("交易") && col.contains("对方") && col.contains("ID") && !existingTargetColumns.contains("交易对方ID")) {
                df = df.withColumnRenamed(col, "交易对方ID");
                logger.info("重命名列: '{}' -> '交易对方ID'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易对方ID");
            }
            else if (col.contains("交易") && col.contains("对方") && col.contains("银行") && col.contains("卡") && !existingTargetColumns.contains("交易对方银行卡号")) {
                df = df.withColumnRenamed(col, "交易对方银行卡号");
                logger.info("重命名列: '{}' -> '交易对方银行卡号'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易对方银行卡号");
            }
            else if (col.contains("交易") && col.contains("业务") && col.contains("类型") && !existingTargetColumns.contains("交易业务类型")) {
                df = df.withColumnRenamed(col, "交易业务类型");
                logger.info("重命名列: '{}' -> '交易业务类型'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易业务类型");
            }
            else if (col.contains("交易") && col.contains("用途") && col.contains("类型") && !existingTargetColumns.contains("交易用途类型")) {
                df = df.withColumnRenamed(col, "交易用途类型");
                logger.info("重命名列: '{}' -> '交易用途类型'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("交易用途类型");
            }
            else if ((col.contains("备注") || col.contains("说明") || col.contains("描述")) && !existingTargetColumns.contains("备注")) {
                df = df.withColumnRenamed(col, "备注");
                logger.info("重命名列: '{}' -> '备注'", col);
                renamedColumns.add(col);
                existingTargetColumns.add("备注");
            }
        }
        for (String col : df.columns()) {
            if (col.equals("交易金额") || col.equals("账户余额")) {
                if (df.first().getAs(col) != null) {
                    String value = df.first().getAs(col).toString();
                    if (value.matches("\\d+") && value.length() > 2) {
                        logger.info("检测到金额单位为分，转换为元: {}", col);
                        df = df.withColumn(col, functions.col(col).divide(100.0));
                    }
                }
            }
        }
        
        // 添加填充银行卡号的功能
        df = fillMissingBankCardNumbers(df);
        
        return df;
    }
    
    /**
     * 为缺失的银行卡号字段填充值
     * 查找每个用户ID对应的非空银行卡号，并用该值填充同一用户ID的其他记录
     */
    private Dataset<Row> fillMissingBankCardNumbers(Dataset<Row> df) {
        if (!Arrays.asList(df.columns()).contains("用户ID")) {
            logger.warn("数据集中没有'用户ID'列，无法补全银行卡号");
            return df;
        }
        
        // 检查是否有银行卡号列
        boolean hasCardNumber = Arrays.asList(df.columns()).contains("用户银行卡号");
        if (!hasCardNumber) {
            logger.warn("数据集中没有'用户银行卡号'列，无法补全");
            return df;
        }
        
        logger.info("开始为缺失的用户银行卡号填充值");
        
        // 创建临时视图以便使用SQL
        df.createOrReplaceTempView("weixin_transactions");
        
        // 1. 创建用户ID到银行卡号的映射表
        Dataset<Row> userCardMapping = spark.sql(
            "SELECT `用户ID`, `用户银行卡号`, COUNT(*) as count " +
            "FROM weixin_transactions " +
            "WHERE `用户银行卡号` IS NOT NULL AND TRIM(`用户银行卡号`) != '' " +
            "GROUP BY `用户ID`, `用户银行卡号` " +
            "ORDER BY `用户ID`, count DESC"
        );
        
        // 将映射表注册为视图
        userCardMapping.createOrReplaceTempView("user_card_mapping");
        
        // 2. 找出每个用户ID的第一个非空银行卡号（按出现频率最高的优先）
        Dataset<Row> userFirstCard = spark.sql(
            "SELECT t1.`用户ID`, t1.`用户银行卡号` " +
            "FROM user_card_mapping t1 " +
            "INNER JOIN (" +
            "    SELECT `用户ID`, MAX(count) as max_count " +
            "    FROM user_card_mapping " +
            "    GROUP BY `用户ID`" +
            ") t2 ON t1.`用户ID` = t2.`用户ID` AND t1.count = t2.max_count"
        );
        
        userFirstCard.createOrReplaceTempView("user_first_card");
        
        // 3. 将用户ID关联到对应的银行卡号，并用它填充缺失值
        Dataset<Row> resultDf = spark.sql(
            "SELECT t.*, " +
            "       CASE WHEN (t.`用户银行卡号` IS NULL OR TRIM(t.`用户银行卡号`) = '') AND c.`用户银行卡号` IS NOT NULL " +
            "            THEN c.`用户银行卡号` " +
            "            ELSE t.`用户银行卡号` " +
            "       END AS filled_card_number " +
            "FROM weixin_transactions t " +
            "LEFT JOIN user_first_card c ON t.`用户ID` = c.`用户ID`"
        );
        
        // 4. 将filled_card_number列重命名为用户银行卡号
        resultDf = resultDf.drop("用户银行卡号").withColumnRenamed("filled_card_number", "用户银行卡号");
        
        // 记录填充情况
        long totalRows = resultDf.count();
        long filledRows = resultDf.filter(functions.col("`用户银行卡号`").isNotNull().and(functions.trim(functions.col("`用户银行卡号`")).notEqual(""))).count();
        
        logger.info("银行卡号字段填充完成。总记录数: {}, 有卡号的记录数: {}, 填充比例: {}%", 
                   totalRows, filledRows, totalRows > 0 ? (filledRows * 100.0 / totalRows) : 0);
        
        return resultDf;
    }

    /**
     * 处理微信数据的特定业务逻辑
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @param columnMappings 列映射
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> processData(Dataset<Row> df, String sourceType, Map<String, String> columnMappings) {
        logger.info("执行微信特有的数据处理逻辑");
        // 调用父类的通用处理逻辑
        return super.processData(df, sourceType, columnMappings);
    }
    
    /**
     * 微信数据后处理
     * @param df 输入数据集
     * @param sourceType 数据源类型
     * @return 处理后的数据集
     */
    @Override
    protected Dataset<Row> postProcess(Dataset<Row> df, String sourceType) {
        // 调用父类的通用处理逻辑
        return super.postProcess(df, sourceType);
    }
} 