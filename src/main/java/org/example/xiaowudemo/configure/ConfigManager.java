package org.example.xiaowudemo.configure;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.Collections;
@Component
@Data
public class ConfigManager {
    private static final Logger logger = LoggerFactory.getLogger(ConfigManager.class);
    private JsonNode config;
    private final Map<String, List<String>> fileTypeKeyWords = new HashMap<>();
    private final Map<String,String> fileTypeOutputDirs = new HashMap<>();
    public ConfigManager(){
        loadConfig();
        parseFileTypes();
    }
    private void loadConfig(){
        try{
            ObjectMapper mapper = new ObjectMapper();
            File configFile = new File("./src/main/resources/data_transform.json");
            if(!configFile.exists()){
                configFile = new File("data_transform_utf8.json");
            }
            config = mapper.readTree(configFile);
            logger.info("加载配置文件成功");
        } catch (IOException e) {
            logger.error("加载配置文件失败:{}",e.getMessage(),e);
        }
    }
    private void parseFileTypes(){
        JsonNode fileTypesNode = config.get("fileTypes");
        if(fileTypesNode!=null && fileTypesNode.isArray()){
            for (JsonNode typeNode : fileTypesNode) {
                String typeName = typeNode.get("name").asText();
                String outputDir = typeNode.get("OutputDir").asText();
                List<String> keyWords = new ArrayList<>();
                JsonNode keyWordsNode = typeNode.get("keyWords");
                if(keyWordsNode!= null && keyWordsNode.isArray()){
                    for (JsonNode keyWord : keyWordsNode)
                    {
                        keyWords.add(keyWord.asText());
                    }
                }
                fileTypeKeyWords.put(typeName,keyWords);
                fileTypeOutputDirs.put(typeName,outputDir);
                logger.info("加载文件类型:{},关键字数量:{},输出目录:{}",typeName,keyWords.size(),outputDir);
            }
        }
    }

    public JsonNode getTemplates(){
        return config.get("templates");
    }
    public JsonNode getTemplateBySource(String sourceType){
        JsonNode templatesNode = getTemplates();
        if(templatesNode!= null&& templatesNode.isArray()){
            for (JsonNode template: templatesNode){
                if(sourceType.equals(template.get("source").asText())){
                    return template;
                }
            }
        }
        return null;
    }
    public List<Map<String, List<String>>> getBankSubTypes(){
        List<Map<String, List<String>>> result = new ArrayList<>();
        JsonNode fileTypesNode = config.get("fileTypes");
        if (fileTypesNode != null && fileTypesNode.isArray()) {
            for (JsonNode typeNode : fileTypesNode) {
                if ("银行".equals(typeNode.get("name").asText()) && typeNode.has("subTypes")) {
                    JsonNode subTypesNode = typeNode.get("subTypes");
                    if (subTypesNode.isArray()) {
                        for (JsonNode subType : subTypesNode) {
                            Map<String, List<String>> subTypeMap = new HashMap<>();
                            String name = subType.get("name").asText();
                            List<String> keyWords = new ArrayList<>();
                            JsonNode keyWordsNode = subType.get("keyWords");
                            if (keyWordsNode != null && keyWordsNode.isArray()) {
                                for (JsonNode keyWord : keyWordsNode) {
                                    keyWords.add(keyWord.asText());
                                }
                            }
                            subTypeMap.put("name", Collections.singletonList(name));
                            subTypeMap.put("keyWords", keyWords);
                            result.add(subTypeMap);
                        }
                    }
                    break;
                }
            }
        }
        return result;
    }
    public List<String> getKeyWordsForFileType(String fileType){
        return fileTypeKeyWords.getOrDefault(fileType,new ArrayList<>());
    }
    public Set<String> getAllFileTypes(){
        return fileTypeKeyWords.keySet();
    }
}

