package org.example.xiaowudemo.repository;

import lombok.AllArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

/**
 * 卡号信息数据访问接口 - 简化版
 * 只保留核心功能：数据写入
 */
@Repository
public class AccountInfoRepository {

    private final JdbcTemplate jdbcTemplate;

    public AccountInfoRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 统计总记录数
     * @return 总记录数量
     */
    public long count() {
        String sql = "SELECT COUNT(*) FROM `卡号信息`";
        return jdbcTemplate.queryForObject(sql, Long.class);
    }

    /**
     * 统计有交易卡号且有证件号码的账户数量（用于联表操作）
     * @return 记录数量
     */
    public long countAccountsForLinking() {
        String sql = """
            SELECT COUNT(*) FROM `卡号信息` 
            WHERE `交易卡号` IS NOT NULL AND `交易卡号` != '' 
            AND `开户人证件号码` IS NOT NULL AND `开户人证件号码` != ''
            """;
        return jdbcTemplate.queryForObject(sql, Long.class);
    }
}