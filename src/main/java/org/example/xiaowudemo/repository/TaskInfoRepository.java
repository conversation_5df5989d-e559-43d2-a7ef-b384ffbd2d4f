package org.example.xiaowudemo.repository;

import lombok.AllArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

/**
 * 任务信息数据访问接口 - 简化版
 * 只保留核心功能：数据写入
 */
@Repository
public class TaskInfoRepository {

    private final JdbcTemplate jdbcTemplate;

    public TaskInfoRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 统计总记录数
     * @return 总记录数量
     */
    public long count() {
        String sql = "SELECT COUNT(*) FROM `任务信息`";
        return jdbcTemplate.queryForObject(sql, Long.class);
    }
}