package org.example.xiaowudemo.repository;

import lombok.AllArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 交易明细数据访问接口 - 简化版
 * 只保留核心功能：数据写入和联表操作
 */
@Repository
public class BankTransactionRepository {

    private final JdbcTemplate jdbcTemplate;

    public BankTransactionRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 核心联表操作：将卡号信息中的开户人证件号码填入交易明细的对手身份证号
     * 通过交易卡号关联两张表
     * 
     * @return 更新的记录数
     */
    @Transactional
    public int updateCounterpartIdFromAccountInfo() {
        String sql = """
            UPDATE `交易明细` t
            JOIN `卡号信息` a ON t.`交易卡号` = a.`交易卡号`
            SET t.`对手身份证号` = a.`开户人证件号码`
            WHERE (t.`对手身份证号` IS NULL OR t.`对手身份证号` = '' OR t.`对手身份证号` = '未知')
            AND a.`开户人证件号码` IS NOT NULL
            AND a.`开户人证件号码` != ''
            """;
        return jdbcTemplate.update(sql);
    }

    /**
     * 查询需要更新对手身份证号的记录数（用于验证联表操作效果）
     * @return 需要更新的记录数
     */
    public long countRecordsNeedingCounterpartIdUpdate() {
        String sql = """
            SELECT COUNT(*) FROM `交易明细` t 
            JOIN `卡号信息` a ON t.`交易卡号` = a.`交易卡号` 
            WHERE (t.`对手身份证号` IS NULL OR t.`对手身份证号` = '' OR t.`对手身份证号` = '未知') 
            AND a.`开户人证件号码` IS NOT NULL 
            AND a.`开户人证件号码` != ''
            """;
        return jdbcTemplate.queryForObject(sql, Long.class);
    }

    /**
     * 统计总记录数
     * @return 总记录数量
     */
    public long count() {
        String sql = "SELECT COUNT(*) FROM `交易明细`";
        return jdbcTemplate.queryForObject(sql, Long.class);
    }
}