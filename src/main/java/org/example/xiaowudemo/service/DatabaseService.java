package org.example.xiaowudemo.service;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;

/**
 * 数据库服务接口
 * 提供Spark DataFrame到数据库的高效写入功能
 */
public interface DatabaseService {

    /**
     * 将交易流水数据写入数据库
     * @param dataFrame Spark DataFrame包含交易流水数据
     * @param dataSource 数据源类型（银行、支付宝、微信等）
     * @return 成功写入的记录数
     * @throws Exception 写入过程中的异常
     */
    long saveTransactionData(Dataset<Row> dataFrame, String dataSource) throws Exception;

    /**
     * 将人员信息数据写入数据库
     * @param dataFrame Spark DataFrame包含人员信息数据
     * @param dataSource 数据源类型
     * @return 成功写入的记录数
     * @throws Exception 写入过程中的异常
     */
    long savePersonnelData(Dataset<Row> dataFrame, String dataSource) throws Exception;

    /**
     * 将任务信息数据写入数据库
     * @param dataFrame Spark DataFrame包含任务信息数据
     * @param dataSource 数据源类型
     * @return 成功写入的记录数
     * @throws Exception 写入过程中的异常
     */
    long saveTaskData(Dataset<Row> dataFrame, String dataSource) throws Exception;

    /**
     * 将账户信息数据写入数据库
     * @param dataFrame Spark DataFrame包含账户信息数据
     * @param dataSource 数据源类型
     * @return 成功写入的记录数
     * @throws Exception 写入过程中的异常
     */
    long saveAccountData(Dataset<Row> dataFrame, String dataSource) throws Exception;

    /**
     * 检查数据库连接状态
     * @return 连接是否正常
     */
    boolean checkDatabaseConnection();

    /**
     * 获取指定表的记录数统计
     * @param tableType 表类型
     * @param dataSource 数据源类型（可选，为null时统计所有数据源）
     * @return 记录数
     */
    long getRecordCount(String tableType, String dataSource);

    /**
     * 批量写入性能测试
     * @param recordCount 测试记录数
     * @return 写入耗时（毫秒）
     */
    long performanceBenchmark(int recordCount);

    /**
     * 执行联表更新操作
     * 将账户信息中的证件号码更新到交易流水表
     * 这是核心的联表操作，用于关联账户信息和交易流水
     * 
     * @return 更新的记录数
     */
    int performJoinTableUpdate();

    /**
     * 获取数据库统计信息
     * @return 统计信息字符串
     */
    String getDatabaseStats();
}