package org.example.xiaowudemo.service;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 优化的数据处理服务
 * 实现按照用户要求的数据写入流程：
 * 1. 先写入四个表
 * 2. 执行联表更新
 * 3. 追加模式，高性能写入
 */
@Service
public class OptimizedDataProcessingService {

    private static final Logger logger = LoggerFactory.getLogger(OptimizedDataProcessingService.class);

    @Autowired
    private DatabaseService databaseService;

    /**
     * 优化的数据处理流程
     * 按照用户要求的顺序执行：账户信息 → 人员信息 → 交易流水 → 任务信息 → 联表更新
     * 
     * @param accountData 账户信息数据
     * @param personnelData 人员信息数据
     * @param transactionData 交易流水数据
     * @param taskData 任务信息数据
     * @param dataSource 数据源标识
     * @return 处理结果统计
     */
    public ProcessingResult processAllData(
            Dataset<Row> accountData,
            Dataset<Row> personnelData, 
            Dataset<Row> transactionData,
            Dataset<Row> taskData,
            String dataSource) {
        
        long totalStartTime = System.currentTimeMillis();
        ProcessingResult result = new ProcessingResult();
        
        logger.info("=== 开始执行优化的数据处理流程，数据源: {} ===", dataSource);
        
        try {
            // 1. 写入账户信息（第一优先级）
            if (accountData != null && accountData.count() > 0) {
                logger.info("步骤1: 写入账户信息数据");
                long accountCount = databaseService.saveAccountData(accountData, dataSource);
                result.setAccountRecords(accountCount);
                logger.info("账户信息写入完成，记录数: {}", accountCount);
            }

            // 2. 写入人员信息（第二优先级）
            if (personnelData != null && personnelData.count() > 0) {
                logger.info("步骤2: 写入人员信息数据");
                long personnelCount = databaseService.savePersonnelData(personnelData, dataSource);
                result.setPersonnelRecords(personnelCount);
                logger.info("人员信息写入完成，记录数: {}", personnelCount);
            }

            // 3. 写入交易流水（第三优先级）
            if (transactionData != null && transactionData.count() > 0) {
                logger.info("步骤3: 写入交易流水数据");
                long transactionCount = databaseService.saveTransactionData(transactionData, dataSource);
                result.setTransactionRecords(transactionCount);
                logger.info("交易流水写入完成，记录数: {}", transactionCount);
            }

            // 4. 写入任务信息（第四优先级）
            if (taskData != null && taskData.count() > 0) {
                logger.info("步骤4: 写入任务信息数据");
                long taskCount = databaseService.saveTaskData(taskData, dataSource);
                result.setTaskRecords(taskCount);
                logger.info("任务信息写入完成，记录数: {}", taskCount);
            }

            // 5. 执行联表更新操作（核心功能）
            logger.info("步骤5: 执行联表更新操作");
            int updatedCount = databaseService.performJoinTableUpdate();
            result.setUpdatedRecords(updatedCount);
            logger.info("联表更新完成，更新记录数: {}", updatedCount);

            // 6. 生成统计报告
            long totalEndTime = System.currentTimeMillis();
            result.setTotalTimeMs(totalEndTime - totalStartTime);
            result.setSuccess(true);
            
            logger.info("=== 数据处理流程完成 ===");
            logger.info("总耗时: {}ms", result.getTotalTimeMs());
            logger.info("处理结果统计:");
            logger.info("  - 账户信息: {} 条", result.getAccountRecords());
            logger.info("  - 人员信息: {} 条", result.getPersonnelRecords());
            logger.info("  - 交易流水: {} 条", result.getTransactionRecords());
            logger.info("  - 任务信息: {} 条", result.getTaskRecords());
            logger.info("  - 联表更新: {} 条", result.getUpdatedRecords());
            
            // 打印数据库统计信息
            String dbStats = databaseService.getDatabaseStats();
            logger.info("\n{}", dbStats);
            
            return result;

        } catch (Exception e) {
            logger.error("数据处理流程失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setTotalTimeMs(System.currentTimeMillis() - totalStartTime);
            return result;
        }
    }

    /**
     * 单独执行联表更新（可用于后续补充更新）
     */
    public int performJoinTableUpdate() {
        logger.info("执行独立的联表更新操作");
        return databaseService.performJoinTableUpdate();
    }

    /**
     * 检查数据库状态
     */
    public boolean checkDatabaseStatus() {
        boolean isConnected = databaseService.checkDatabaseConnection();
        logger.info("数据库连接状态: {}", isConnected ? "正常" : "异常");
        return isConnected;
    }

    /**
     * 获取数据库统计信息
     */
    public String getDatabaseStatistics() {
        return databaseService.getDatabaseStats();
    }

    /**
     * 处理结果统计类
     */
    public static class ProcessingResult {
        private boolean success;
        private String errorMessage;
        private long totalTimeMs;
        private long accountRecords;
        private long personnelRecords;
        private long transactionRecords;
        private long taskRecords;
        private int updatedRecords;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public long getTotalTimeMs() { return totalTimeMs; }
        public void setTotalTimeMs(long totalTimeMs) { this.totalTimeMs = totalTimeMs; }

        public long getAccountRecords() { return accountRecords; }
        public void setAccountRecords(long accountRecords) { this.accountRecords = accountRecords; }

        public long getPersonnelRecords() { return personnelRecords; }
        public void setPersonnelRecords(long personnelRecords) { this.personnelRecords = personnelRecords; }

        public long getTransactionRecords() { return transactionRecords; }
        public void setTransactionRecords(long transactionRecords) { this.transactionRecords = transactionRecords; }

        public long getTaskRecords() { return taskRecords; }
        public void setTaskRecords(long taskRecords) { this.taskRecords = taskRecords; }

        public int getUpdatedRecords() { return updatedRecords; }
        public void setUpdatedRecords(int updatedRecords) { this.updatedRecords = updatedRecords; }

        public long getTotalRecords() {
            return accountRecords + personnelRecords + transactionRecords + taskRecords;
        }

        @Override
        public String toString() {
            return String.format(
                "ProcessingResult{success=%s, totalTime=%dms, records={account=%d, personnel=%d, transaction=%d, task=%d, updated=%d}, total=%d}",
                success, totalTimeMs, accountRecords, personnelRecords, transactionRecords, taskRecords, updatedRecords, getTotalRecords()
            );
        }
    }
}