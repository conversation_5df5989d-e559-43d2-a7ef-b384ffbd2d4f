package org.example.xiaowudemo.service.impl;
import org.example.xiaowudemo.service.FileService;
import org.example.xiaowudemo.service.SparkService;
import org.example.xiaowudemo.util.FileTypeDetector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
public class FileServiceImpl implements FileService {
    private static final Logger logger = LoggerFactory.getLogger(FileServiceImpl.class);
    @Value("${app.upload-dir:./upload}")
    private String uploadDir;
    
    @Value("${app.classified-dir:./classified}")
    private String classifiedDir;
    
    @Value("${app.output-dir:./output}")
    private String outputDir;
    
    private static final List<String> SUPPORTED_EXTENSIONS = Arrays.asList("csv", "xlsx", "txt", "zip");
    private final FileTypeDetector fileTypeDetector;
    private final SparkService sparkService;
    
    // 添加处理状态标志，防止重复处理
    private static final AtomicBoolean isProcessing = new AtomicBoolean(false);
    
    @Autowired
    public FileServiceImpl(FileTypeDetector fileTypeDetector, SparkService sparkService) {
        this.fileTypeDetector = fileTypeDetector;
        this.sparkService = sparkService;
    }
    
    @Override
    public void processInputDirectory(String inputDir) throws Exception {
        // 检查是否已经在处理中，避免重复处理
        if (!isProcessing.compareAndSet(false, true)) {
            logger.warn("已有处理任务在运行中，跳过本次处理请求: {}", inputDir);
            return;
        }
        
        try {
            logger.info("开始处理输入目录:{}", inputDir);
            File inputDirFile = new File(inputDir);
            if(!inputDirFile.exists() || !inputDirFile.isDirectory()) {
                throw new IllegalArgumentException("输入的目录不存在或者不是有效目录:" + inputDir);
            }
            ensureDirectoryExists(uploadDir);
            ensureDirectoryExists(classifiedDir);
            ensureDirectoryExists(outputDir);
            
            // SparkService的目录已通过@Value注解自动注入，无需手动设置
            
            List<File> allFiles = collectFiles(inputDirFile);
            logger.info("在输入目录中找到{}个文件", allFiles.size());
            if(allFiles.isEmpty()) {
                logger.warn("输入目录是空的，没有文件需要处理");
                return;
            }
            Map<String,List<File>> classifiedFiles = classifyFiles(allFiles);
            // 使用SparkService处理文件
            sparkService.transformFiles(classifiedFiles);
            logger.info("输入目录处理完成:{}", inputDir);
            // 设置处理完成标志
            setProcessingComplete();
        } finally {
            // 无论成功还是失败，都重置处理标志
            isProcessing.set(false);
        }
    }
    
    private void ensureDirectoryExists(String dirPath) throws Exception{
        Files.createDirectories(Paths.get(dirPath));
        logger.info("确保目录存在:{}", dirPath);
    }
    
    private List<File> collectFiles(File directory){
        List<File> files = new ArrayList<>();
        File[] entries = directory.listFiles();
        if(entries != null)
        {
            for (File entry: entries)
            {
                if(entry.isFile())
                {
                    String extension = getFileExtension(entry.getName()).toLowerCase();
                    if(SUPPORTED_EXTENSIONS.contains(extension)){
                        files.add(entry);
                    }
                }else if(entry.isDirectory()){
                    files.addAll(collectFiles(entry));
                }
            }
        }
        return files;
    }
    
    private Map<String, List<File>> classifyFiles(List<File> files) throws Exception {
        Map<String, List<File>> classifiedFiles = new HashMap<>();
        for (File file : files) {
            String fileType = fileTypeDetector.detectFileType(file);
            logger.info("文件 {} 被识别为 {} 类型", file.getName(), fileType);
            classifiedFiles.computeIfAbsent(fileType, k -> new ArrayList<>()).add(file);
            copyFileToClassifiedDir(file, fileType);
        }
        return classifiedFiles;
    }
    
    private void copyFileToClassifiedDir(File file, String fileType) throws Exception {
        String extension = getFileExtension(file.getName().toLowerCase());
        Path targetDir = Paths.get(classifiedDir, fileType, extension);
        if (!Files.exists(targetDir)) {
            Files.createDirectories(targetDir);
            logger.info("创建目录: {}", targetDir);
        }
        Path targetPath = targetDir.resolve(file.getName());
        Files.copy(file.toPath(), targetPath, StandardCopyOption.REPLACE_EXISTING);
        logger.info("文件{}复制到{}", file.getName(), targetPath);
    }
    
    private static String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex > 0) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }
    
    /**
     * 设置处理完成标志，通知应用程序可以退出
     */
    private void setProcessingComplete() {
        logger.info("所有文件处理完成，设置处理完成标志");
        // 确保在设置属性之前释放所有资源
        try {
            if (sparkService instanceof SparkServiceImpl) {
                logger.info("主动调用sparkService.cleanup()清理Spark资源");
                ((SparkServiceImpl) sparkService).cleanup();
            }
        } catch (Exception e) {
            logger.error("清理Spark资源时发生错误: {}", e.getMessage(), e);
        }
        
        // 设置处理完成标志
        System.setProperty("app.processing.complete", "true");
        logger.info("处理完成标志已设置，应用程序将会在几秒后退出");
    }
}