package org.example.xiaowudemo.service.impl;

import lombok.Data;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SaveMode;
import org.example.xiaowudemo.config.DatabaseConfig;
import org.example.xiaowudemo.repository.AccountInfoRepository;
import org.example.xiaowudemo.repository.BankTransactionRepository;
import org.example.xiaowudemo.repository.PersonnelInfoRepository;
import org.example.xiaowudemo.repository.TaskInfoRepository;
import org.example.xiaowudemo.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import org.apache.spark.sql.functions;

/**
 * 数据库服务实现类 - 优化版
 * 专注于高性能追加写入和联表操作
 */
@Service
@Data
public class DatabaseServiceImpl implements DatabaseService {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseServiceImpl.class);

    private final BankTransactionRepository bankTransactionRepository;

    private final PersonnelInfoRepository personnelInfoRepository;

    private final TaskInfoRepository taskInfoRepository;

    private final AccountInfoRepository accountInfoRepository;

    private final DataSource dataSource;

    private final DatabaseConfig databaseConfig;

    @Value("${spring.datasource.url}")
    private String jdbcUrl;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    public DatabaseServiceImpl(BankTransactionRepository bankTransactionRepository,
                             PersonnelInfoRepository personnelInfoRepository,
                             TaskInfoRepository taskInfoRepository,
                             AccountInfoRepository accountInfoRepository,
                             DataSource dataSource,
                             DatabaseConfig databaseConfig) {
        this.bankTransactionRepository = bankTransactionRepository;
        this.personnelInfoRepository = personnelInfoRepository;
        this.taskInfoRepository = taskInfoRepository;
        this.accountInfoRepository = accountInfoRepository;
        this.dataSource = dataSource;
        this.databaseConfig = databaseConfig;
    }

    /**
     * 写入账户信息数据
     */
    @Override
    @Transactional
    public long saveAccountData(Dataset<Row> dataFrame, String dataSource) throws Exception {
        long startTime = System.currentTimeMillis();
        logger.info("开始写入账户信息数据，来源: {}", dataSource);

        try {
            // 处理NULL值，确保必要的列有默认值
            dataFrame = preprocessAccountData(dataFrame);
            
            // 获取账户信息表的实际列名
            dataFrame = selectAccountColumns(dataFrame);
            
            // 直接写入数据库，不添加数据源字段
            Properties connectionProperties = getConnectionProperties();
            dataFrame.write()
                .mode(SaveMode.Append)  // 使用追加模式
                .option("batchsize", "1000")
                .option("isolationLevel", "READ_COMMITTED")
                .option("createTableOptions", "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci")
                .jdbc(jdbcUrl, "卡号信息", connectionProperties);

            long recordCount = dataFrame.count();
            long endTime = System.currentTimeMillis();
            
            logger.info("账户信息数据写入完成，记录数: {}，耗时: {}ms", recordCount, (endTime - startTime));
            return recordCount;

        } catch (Exception e) {
            logger.error("写入账户信息数据失败: {}", e.getMessage(), e);
            throw new Exception("写入账户信息数据失败", e);
        }
    }

    /**
     * 写入人员信息数据
     */
    @Override
    @Transactional
    public long savePersonnelData(Dataset<Row> dataFrame, String dataSource) throws Exception {
        long startTime = System.currentTimeMillis();
        logger.info("开始写入人员信息数据，来源: {}", dataSource);

        try {
            // 处理NULL值，确保必要的列有默认值
            dataFrame = preprocessPersonnelData(dataFrame);
            
            // 获取人员信息表的实际列名
            dataFrame = selectPersonnelColumns(dataFrame);
            
            Properties connectionProperties = getConnectionProperties();
            dataFrame.write()
                .mode(SaveMode.Append)  // 使用追加模式
                .option("batchsize", "1000")
                .option("isolationLevel", "READ_COMMITTED")
                .option("createTableOptions", "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci")
                .jdbc(jdbcUrl, "人员信息", connectionProperties);

            long recordCount = dataFrame.count();
            long endTime = System.currentTimeMillis();
            
            logger.info("人员信息数据写入完成，记录数: {}，耗时: {}ms", recordCount, (endTime - startTime));
            return recordCount;

        } catch (Exception e) {
            logger.error("写入人员信息数据失败: {}", e.getMessage(), e);
            throw new Exception("写入人员信息数据失败", e);
        }
    }

    /**
     * 写入交易流水数据
     */
    @Override
    @Transactional
    public long saveTransactionData(Dataset<Row> dataFrame, String dataSource) throws Exception {
        long startTime = System.currentTimeMillis();
        logger.info("开始写入交易流水数据，来源: {}", dataSource);

        try {
            // 处理NULL值，确保必要的列有默认值
            dataFrame = preprocessTransactionData(dataFrame);
            
            Properties connectionProperties = getConnectionProperties();
            dataFrame.write()
                .mode(SaveMode.Append)  // 使用追加模式
                .option("batchsize", "1000")
                .option("isolationLevel", "READ_COMMITTED")
                .option("createTableOptions", "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci")
                .jdbc(jdbcUrl, "交易明细", connectionProperties);

            long recordCount = dataFrame.count();
            long endTime = System.currentTimeMillis();
            
            logger.info("交易流水数据写入完成，记录数: {}，耗时: {}ms", recordCount, (endTime - startTime));
            return recordCount;

        } catch (Exception e) {
            logger.error("写入交易流水数据失败: {}", e.getMessage(), e);
            throw new Exception("写入交易流水数据失败", e);
        }
    }

    /**
     * 预处理交易流水数据，确保NULL值有合适的默认值
     * @param dataFrame 原始数据框
     * @return 处理后的数据框
     */
    private Dataset<Row> preprocessTransactionData(Dataset<Row> dataFrame) {
        logger.debug("开始预处理交易流水数据，处理NULL值");
        
        // 定义必须有默认值的列及其默认值
        Map<String, String> defaultValues = new HashMap<>();
        defaultValues.put("对手开户银行", "");  // 空字符串而非NULL
        defaultValues.put("对手户名", "");
        defaultValues.put("对手身份证号", "");
        defaultValues.put("交易对手账卡号", "");
        defaultValues.put("现金标志", "");
        defaultValues.put("交易币种", "");
        defaultValues.put("交易网点名称", "");
        defaultValues.put("交易发生地", "");
        defaultValues.put("交易是否成功", "");
        defaultValues.put("传票号", "");
        defaultValues.put("IP地址", "");
        defaultValues.put("MAC地址", "");
        defaultValues.put("对手交易余额", "");
        defaultValues.put("交易流水号", "");
        defaultValues.put("日志号", "");
        defaultValues.put("凭证种类", "");
        defaultValues.put("凭证号", "");
        defaultValues.put("交易柜员号", "");
        defaultValues.put("备注", "");
        defaultValues.put("商户名称", "");
        defaultValues.put("交易类型", "");
        defaultValues.put("查询反馈结果原因", "");
        defaultValues.put("开户行", "");
        defaultValues.put("交易方证件号码", ""); // 添加交易方证件号码默认值处理
        
        // 为每个列添加默认值处理
        for (Map.Entry<String, String> entry : defaultValues.entrySet()) {
            String columnName = entry.getKey();
            String defaultValue = entry.getValue();
            
            // 如果列存在，则填充NULL值；如果不存在，则添加该列
            if (Arrays.asList(dataFrame.columns()).contains(columnName)) {
                dataFrame = dataFrame.withColumn(columnName, 
                    functions.when(functions.col(columnName).isNull().or(functions.col(columnName).equalTo("")), 
                        functions.lit(defaultValue))
                    .otherwise(functions.col(columnName)));
            } else {
                // 如果列不存在，直接添加默认值列
                dataFrame = dataFrame.withColumn(columnName, functions.lit(defaultValue));
            }
        }
        
        logger.debug("交易流水数据预处理完成");
        return dataFrame;
    }

    /**
     * 预处理账户信息数据，确保NULL值有合适的默认值
     * @param dataFrame 原始数据框
     * @return 处理后的数据框
     */
    private Dataset<Row> preprocessAccountData(Dataset<Row> dataFrame) {
        logger.debug("开始预处理账户信息数据，处理NULL值");
        
        // 定义必须有默认值的列及其默认值
        Map<String, String> defaultValues = new HashMap<>();
        defaultValues.put("账户开户名称", "");
        defaultValues.put("开户人证件号码", "");
        defaultValues.put("交易卡号", "");
        defaultValues.put("交易账号", "");
        defaultValues.put("账号开户时间", "");
        defaultValues.put("账户余额", "");
        defaultValues.put("可用余额", "");
        defaultValues.put("币种", "");
        defaultValues.put("开户网点代码", "");
        defaultValues.put("开户网点", "");
        defaultValues.put("账户状态", "");
        defaultValues.put("钞汇标志名称", "");
        defaultValues.put("开户人证件类型", "");
        defaultValues.put("销户日期", "");
        defaultValues.put("账户类型", "");
        defaultValues.put("开户联系方式", "");
        defaultValues.put("通信地址", "");
        defaultValues.put("联系电话", "");
        defaultValues.put("代理人", "");
        defaultValues.put("代理人电话", "");
        defaultValues.put("备注", "");
        defaultValues.put("开户省份", "");
        defaultValues.put("开户城市", "");
        defaultValues.put("账号开户银行", "");
        defaultValues.put("客户代码", "");
        defaultValues.put("法人代表", "");
        defaultValues.put("客户工商执照号码", "");
        defaultValues.put("法人代表证件号码", "");
        defaultValues.put("住宅地址", "");
        defaultValues.put("邮政编码", "");
        defaultValues.put("代办人证件号码", "");
        defaultValues.put("邮箱地址", "");
        defaultValues.put("关联资金账户", "");
        defaultValues.put("地税纳税号", "");
        defaultValues.put("单位电话", "");
        defaultValues.put("代办人证件类型", "");
        defaultValues.put("住宅电话", "");
        defaultValues.put("法人代表证件类型", "");
        defaultValues.put("国税纳税号", "");
        defaultValues.put("单位地址", "");
        defaultValues.put("工作单位", "");
        defaultValues.put("销户网点", "");
        defaultValues.put("最后交易时间", "");
        defaultValues.put("账户销户银行", "");
        defaultValues.put("任务流水号", "");
        defaultValues.put("文件名", "");
        defaultValues.put("文件路径", "");
        defaultValues.put("生活轨迹", "");
        defaultValues.put("夜间交易", "");
        defaultValues.put("小额测试", "");
        defaultValues.put("外汇", "");
        defaultValues.put("虚拟币", "");
        defaultValues.put("存现", "");
        defaultValues.put("取现", "");
        defaultValues.put("快进快出", "");
        
        // 为每个列添加默认值处理
        for (Map.Entry<String, String> entry : defaultValues.entrySet()) {
            String columnName = entry.getKey();
            String defaultValue = entry.getValue();
            
            // 如果列存在，则填充NULL值；如果不存在，则添加该列
            if (Arrays.asList(dataFrame.columns()).contains(columnName)) {
                dataFrame = dataFrame.withColumn(columnName, 
                    functions.when(functions.col(columnName).isNull().or(functions.col(columnName).equalTo("")), 
                        functions.lit(defaultValue))
                    .otherwise(functions.col(columnName)));
            } else {
                // 如果列不存在，直接添加默认值列
                dataFrame = dataFrame.withColumn(columnName, functions.lit(defaultValue));
            }
        }
        
        logger.debug("账户信息数据预处理完成");
        return dataFrame;
    }

    /**
     * 预处理人员信息数据，确保NULL值有合适的默认值
     * @param dataFrame 原始数据框
     * @return 处理后的数据框
     */
    private Dataset<Row> preprocessPersonnelData(Dataset<Row> dataFrame) {
        logger.debug("开始预处理人员信息数据，处理NULL值");
        
        // 定义必须有默认值的列及其默认值
        Map<String, String> defaultValues = new HashMap<>();
        defaultValues.put("客户名称", "");
        defaultValues.put("证照类型", "");
        defaultValues.put("证照号码", "");
        defaultValues.put("单位地址", "");
        defaultValues.put("联系电话", "");
        defaultValues.put("联系手机", "");
        defaultValues.put("单位电话", "");
        defaultValues.put("住宅电话", "");
        defaultValues.put("工作单位", "");
        defaultValues.put("邮箱地址", "");
        defaultValues.put("代办人姓名", "");
        defaultValues.put("代办人证件类型", "");
        defaultValues.put("代办人证件号码", "");
        defaultValues.put("国税纳税号", "");
        defaultValues.put("地税纳税号", "");
        defaultValues.put("法人代表", "");
        defaultValues.put("法人代表证件类型", "");
        defaultValues.put("法人代表证件号码", "");
        defaultValues.put("出生日期", "");
        defaultValues.put("户籍地址", "");
        defaultValues.put("客户工商执照号码", "");
        defaultValues.put("文件名", "");
        defaultValues.put("文件路径", "");
        
        // 为每个列添加默认值处理
        for (Map.Entry<String, String> entry : defaultValues.entrySet()) {
            String columnName = entry.getKey();
            String defaultValue = entry.getValue();
            
            // 如果列存在，则填充NULL值；如果不存在，则添加该列
            if (Arrays.asList(dataFrame.columns()).contains(columnName)) {
                dataFrame = dataFrame.withColumn(columnName, 
                    functions.when(functions.col(columnName).isNull().or(functions.col(columnName).equalTo("")), 
                        functions.lit(defaultValue))
                    .otherwise(functions.col(columnName)));
            } else {
                // 如果列不存在，直接添加默认值列
                dataFrame = dataFrame.withColumn(columnName, functions.lit(defaultValue));
            }
        }
        
        logger.debug("人员信息数据预处理完成");
        return dataFrame;
    }

    /**
     * 写入任务信息数据（如果需要的话）
     */
    @Override
    @Transactional
    public long saveTaskData(Dataset<Row> dataFrame, String dataSource) throws Exception {
        long startTime = System.currentTimeMillis();
        logger.info("开始写入任务信息数据，来源: {}", dataSource);

        try {
            Properties connectionProperties = getConnectionProperties();
            dataFrame.write()
                .mode(SaveMode.Append)  // 使用追加模式
                .option("batchsize", "1000")
                .option("isolationLevel", "READ_COMMITTED")
                .option("createTableOptions", "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci")
                .jdbc(jdbcUrl, "任务信息", connectionProperties);

            long recordCount = dataFrame.count();
            long endTime = System.currentTimeMillis();
            
            logger.info("任务信息数据写入完成，记录数: {}，耗时: {}ms", recordCount, (endTime - startTime));
            return recordCount;

        } catch (Exception e) {
            logger.error("写入任务信息数据失败: {}", e.getMessage(), e);
            throw new Exception("写入任务信息数据失败", e);
        }
    }

    /**
     * 执行核心联表更新操作
     * 将账户信息中的开户人证件号码填入交易流水的空缺对手身份证号
     */
    @Transactional
    public int performJoinTableUpdate() {
        long startTime = System.currentTimeMillis();
        logger.info("开始执行联表更新操作...");

        try {
            // 1. 查询需要更新的记录数
            long needUpdateCount = bankTransactionRepository.countRecordsNeedingCounterpartIdUpdate();
            logger.info("发现需要更新对手身份证号的交易记录数: {}", needUpdateCount);

            if (needUpdateCount == 0) {
                logger.info("没有需要更新的记录");
                return 0;
            }

            // 2. 执行联表更新
            int updatedCount = bankTransactionRepository.updateCounterpartIdFromAccountInfo();
            long endTime = System.currentTimeMillis();

            logger.info("联表更新操作完成，更新记录数: {}，耗时: {}ms", updatedCount, (endTime - startTime));
            return updatedCount;

        } catch (Exception e) {
            logger.error("联表更新操作失败: {}", e.getMessage(), e);
            throw new RuntimeException("联表更新操作失败", e);
        }
    }

    /**
     * 检查数据库连接状态
     */
    @Override
    public boolean checkDatabaseConnection() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5);
        } catch (SQLException e) {
            logger.error("数据库连接检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取指定表的记录数统计
     */
    @Override
    public long getRecordCount(String tableType, String dataSource) {
        try {
            return switch (tableType.toLowerCase()) {
                case "account", "账户信息", "账户信息表" -> accountInfoRepository.count();
                case "personnel", "人员信息", "人员信息表" -> personnelInfoRepository.count();
                case "transaction", "交易流水", "交易流水表" -> bankTransactionRepository.count();
                case "task", "任务信息", "任务信息表" -> taskInfoRepository.count();
                default -> 0;
            };
        } catch (Exception e) {
            logger.error("获取记录数失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 性能基准测试
     */
    @Override
    public long performanceBenchmark(int recordCount) {
        logger.info("开始数据库写入性能测试，记录数: {}", recordCount);
        long startTime = System.currentTimeMillis();
        //TODO:这里可以实现性能测试
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        logger.info("性能测试完成，耗时: {}ms", duration);
        return duration;
    }

    /**
     * 获取数据库连接属性
     */
    private Properties getConnectionProperties() {
        Properties props = new Properties();
        props.setProperty("user", username);
        props.setProperty("password", password);
        props.setProperty("driver", "com.mysql.cj.jdbc.Driver");
        props.setProperty("useUnicode", "true");
        props.setProperty("characterEncoding", "utf8");
        props.setProperty("useSSL", "false");
        props.setProperty("allowPublicKeyRetrieval", "true");
        props.setProperty("rewriteBatchedStatements", "true");
        props.setProperty("useServerPrepStmts", "false");
        return props;
    }

    /**
     * 获取数据库写入统计信息
     */
    public String getDatabaseStats() {
        return "=== 数据库统计信息 ===\n" +
                String.format("账户信息表记录数: %d\n", getRecordCount("account", null)) +
                String.format("人员信息表记录数: %d\n", getRecordCount("personnel", null)) +
                String.format("交易流水表记录数: %d\n", getRecordCount("transaction", null)) +
                String.format("任务信息表记录数: %d\n", getRecordCount("task", null)) +
                String.format("可联表更新的账户数: %d\n", accountInfoRepository.countAccountsForLinking()) +
                String.format("需要更新对手身份证号的交易记录数: %d\n", bankTransactionRepository.countRecordsNeedingCounterpartIdUpdate());
    }

    /**
     * 选择账户信息表对应的列
     * @param dataFrame 原始数据框
     * @return 只包含账户信息表列的数据框
     */
    private Dataset<Row> selectAccountColumns(Dataset<Row> dataFrame) {
        // 定义账户信息表的实际列名（基于schema.sql中的卡号信息表）
        String[] targetColumns = {
            "账户开户名称", "开户人证件号码", "交易卡号", "交易账号", "账号开户时间",
            "账户余额", "可用余额", "币种", "开户网点代码", "开户网点", "账户状态", 
            "钞汇标志名称", "开户人证件类型", "销户日期", "账户类型", "开户联系方式", 
            "通信地址", "联系电话", "代理人", "代理人电话", "备注", "开户省份", 
            "开户城市", "账号开户银行", "客户代码", "法人代表", "客户工商执照号码", 
            "法人代表证件号码", "住宅地址", "邮政编码", "代办人证件号码", "邮箱地址", 
            "关联资金账户", "地税纳税号", "单位电话", "代办人证件类型", "住宅电话", 
            "法人代表证件类型", "国税纳税号", "单位地址", "工作单位", "销户网点", 
            "最后交易时间", "账户销户银行", "任务流水号", "文件名", "文件路径", 
            "生活轨迹", "夜间交易", "小额测试", "外汇", "虚拟币", "存现", "取现", "快进快出"
        };
        
        // 只选择存在的列
        java.util.List<String> existingColumns = new java.util.ArrayList<>();
        for (String col : targetColumns) {
            if (java.util.Arrays.asList(dataFrame.columns()).contains(col)) {
                existingColumns.add(col);
            }
        }
        
        if (existingColumns.isEmpty()) {
            logger.warn("警告：没有找到匹配的账户信息列，返回原始数据框");
            return dataFrame;
        }
        
        logger.debug("选择账户信息表列，共 {} 列", existingColumns.size());
        if (existingColumns.size() == 1) {
            return dataFrame.select(existingColumns.get(0));
        } else {
            String first = existingColumns.get(0);
            String[] rest = existingColumns.subList(1, existingColumns.size()).toArray(new String[0]);
            return dataFrame.select(first, rest);
        }
    }

    /**
     * 选择人员信息表对应的列
     * @param dataFrame 原始数据框
     * @return 只包含人员信息表列的数据框
     */
    private Dataset<Row> selectPersonnelColumns(Dataset<Row> dataFrame) {
        // 定义人员信息表的实际列名（基于schema.sql中的人员信息表）
        String[] targetColumns = {
            "客户名称", "证照类型", "证照号码", "单位地址", "联系电话", "联系手机",
            "单位电话", "住宅电话", "工作单位", "邮箱地址", "代办人姓名", "代办人证件类型",
            "代办人证件号码", "国税纳税号", "地税纳税号", "法人代表", "法人代表证件类型",
            "法人代表证件号码", "出生日期", "户籍地址", "客户工商执照号码", "文件名", "文件路径"
        };
        
        // 只选择存在的列
        java.util.List<String> existingColumns = new java.util.ArrayList<>();
        for (String col : targetColumns) {
            if (java.util.Arrays.asList(dataFrame.columns()).contains(col)) {
                existingColumns.add(col);
            }
        }
        
        if (existingColumns.isEmpty()) {
            logger.warn("警告：没有找到匹配的人员信息列，返回原始数据框");
            return dataFrame;
        }
        
        logger.debug("选择人员信息表列，共 {} 列", existingColumns.size());
        if (existingColumns.size() == 1) {
            return dataFrame.select(existingColumns.get(0));
        } else {
            String first = existingColumns.get(0);
            String[] rest = existingColumns.subList(1, existingColumns.size()).toArray(new String[0]);
            return dataFrame.select(first, rest);
        }
    }
}