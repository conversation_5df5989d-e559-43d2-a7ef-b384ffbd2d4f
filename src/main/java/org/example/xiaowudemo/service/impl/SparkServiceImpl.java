package org.example.xiaowudemo.service.impl;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.Column;
import org.apache.spark.sql.SaveMode;
import org.apache.spark.sql.functions;
import org.example.xiaowudemo.configure.ConfigManager;
import org.example.xiaowudemo.service.SparkService;
import org.example.xiaowudemo.service.DatabaseService;
import org.example.xiaowudemo.transformData.DataTransformer;
import org.example.xiaowudemo.transformData.DataTransformerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@Service
@Data
public class SparkServiceImpl implements SparkService {
    private static final Logger logger = LoggerFactory.getLogger(SparkServiceImpl.class);
    
    private final ConfigManager configManager;
    
    private SparkSession sparkSession;

    private  final DatabaseService databaseService;
    
    @Value("${spark.app.name:文件转换}")
    private String sparkAppName;
    
    @Value("${spark.memory.offHeap.enabled:true}")
    private String sparkOffHeapEnabled;
    
    @Value("${spark.memory.offHeap.size:2g}")
    private String sparkOffHeapSize;
    
    @Value("${spark.executor.memory:4g}")
    private String sparkExecutorMemory;
    
    @Value("${spark.driver.memory:4g}")
    private String sparkDriverMemory;
    
    @Value("${spark.default.parallelism:20}")
    private String sparkDefaultParallelism;
    
    @Value("${spark.sql.shuffle.partitions:20}")
    private String sparkShufflePartitions;
    
    @Setter
    @Value("${app.classified-dir:./classified}")
    private String classifiedDir;
    
    @Setter
    @Value("${app.output-dir:./output}")
    private String outputDir;
    
    @Autowired
    public SparkServiceImpl(ConfigManager configManager, DatabaseService databaseService) {
        this.configManager = configManager;
        // 不在构造函数中创建SparkSession，而是等待@PostConstruct方法
        this.databaseService = databaseService;
    }
    
    /**
     * 在所有属性注入完成后初始化SparkSession
     */
    @PostConstruct
    public void init() {
        logger.info("初始化SparkSession...");
        try {
            this.sparkSession = createSparkSession();
            logger.info("SparkSession初始化成功");
        } catch (Exception e) {
            logger.error("SparkSession初始化失败: {}", e.getMessage(), e);
        }
    }
    
    private SparkSession createSparkSession() {
        logger.info("创建SparkSession，参数：appName={}, offHeapEnabled={}, offHeapSize={}, " +
                "executorMemory={}, driverMemory={}, parallelism={}, shufflePartitions={}",
                sparkAppName, sparkOffHeapEnabled, sparkOffHeapSize,
                sparkExecutorMemory, sparkDriverMemory, sparkDefaultParallelism,
                sparkShufflePartitions);
                
        return SparkSession.builder()
                .appName(sparkAppName)
                .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
                .config("spark.kryo.registrationRequired", "false")
                .config("spark.memory.offHeap.enabled", sparkOffHeapEnabled)
                .config("spark.memory.offHeap.size", sparkOffHeapSize)
                .config("spark.executor.memory", sparkExecutorMemory)
                .config("spark.driver.memory", sparkDriverMemory)
                .config("spark.default.parallelism", sparkDefaultParallelism)
                .config("spark.sql.shuffle.partitions", sparkShufflePartitions)
                .config("spark.sql.adaptive.enabled", "true")
                .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
                .config("spark.sql.inMemoryColumnarStorage.compressed", "true")
                .config("spark.sql.files.maxPartitionBytes", "128m")
                .config("spark.sql.parquet.filterPushdown", "true")
                .config("spark.sql.parquet.mergeSchema", "false")
                .master("local[*]")
                .config("spark.driver.allowMultipleContexts", "true")
                .getOrCreate();
    }

    @Override
    public SparkSession getSparkSession() {
        if (sparkSession == null || sparkSession.sparkContext().isStopped()) {
            sparkSession = createSparkSession();
        }
        return sparkSession;
    }

    @Override
    public void transformFiles(Map<String, List<File>> classifiedFiles) {
        logger.info("开始转换文件");
        try {
            SparkSession spark = getSparkSession();
            
            for (String fileType : classifiedFiles.keySet()) {
                try {
                    logger.info("处理文件类型:{}", fileType);
                    DataTransformer transformer = DataTransformerFactory.createTransformer(
                            fileType, spark, classifiedDir, databaseService
                    );
                    transformer.transform(fileType);
                    logger.info("{}类型的文件转换完成", fileType);
                } catch (Exception e) {
                    logger.error("处理文件类型:{}失败:{}", fileType, e.getMessage(), e);
                }
            }
            enrichBankTransactionWithAccountInfo();
            mergeTransactionData();
            cleanup();
            
            // 数据库统计信息
            if (databaseService != null) {
                logDatabaseStatistics();
            }
        } catch (Exception e) {
            logger.error("转换文件失败:{}", e.getMessage(), e);
        }
    }

    @Override
    public void mergeTransactionData() {
        logger.info("开始合并交易流水数据");
        try {
            SparkSession spark = getSparkSession();
            setupSparkConfig(spark);
            Path transactionOutputPath = Paths.get(outputDir, "交易流水");
            if (!Files.exists(transactionOutputPath)) {
                Files.createDirectories(transactionOutputPath);
            }
            // 收集所有parquet文件及其元数据
            List<ParquetFileInfo> parquetFiles = collectParquetFiles();
            if (parquetFiles.isEmpty()) {
                handleEmptyParquetFiles();
                return;
            }
            logger.info("开始直接合并所有数据文件...");
            Dataset<Row> mergedData = processAndMergeFiles(spark, parquetFiles);
            if (mergedData != null) {
                saveMergedData(mergedData, transactionOutputPath);
            } else {
                logger.warn("没有成功合并任何数据");
            }
        } catch (Exception e) {
            logger.error("合并交易流水数据失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 设置Spark配置以处理大数据集
     */
    private void setupSparkConfig(SparkSession spark) {
        spark.conf().set("spark.sql.files.maxPartitionBytes", "1g");
        spark.conf().set("spark.sql.files.openCostInBytes", "1g");
        spark.conf().set("spark.sql.broadcastTimeout", "3600");
        spark.conf().set("spark.sql.autoBroadcastJoinThreshold", "-1"); // 禁用广播连接
        spark.conf().set("spark.sql.shuffle.partitions", "1"); // 使用单个分区
    }
    
    /**
     * 收集Parquet文件及其元数据
     */
    private List<ParquetFileInfo> collectParquetFiles() throws Exception {
        List<ParquetFileInfo> parquetFiles = new ArrayList<>();
        String[] transactionTypes = {"银行_交易流水_填充", "支付宝", "微信"};
        // 首先检查transformed目录是否存在
        Path transformedPath = Paths.get(classifiedDir, "transformed");
        if (!Files.exists(transformedPath)) {
            logger.warn("转换后的数据目录不存在: {}, 创建目录", transformedPath);
            Files.createDirectories(transformedPath);
        }
        SparkSession spark = getSparkSession();
        for (String type : transactionTypes) {
            Path typePath = Paths.get(classifiedDir, "transformed", type);
            if (Files.exists(typePath)) {
                Files.walk(typePath)
                        .filter(path -> path.toString().endsWith(".parquet"))
                        .forEach(path -> {
                            try {
                                long fileSize = Files.size(path);
                                long creationTime = Files.getLastModifiedTime(path).toMillis();
                                Dataset<Row> df = readAndProcessFile(path.toString(), spark);
                                long rowCount = df.count();
                                double efficiency = (double) rowCount / fileSize;
                                parquetFiles.add(new ParquetFileInfo(
                                        path.toString(),
                                        fileSize,
                                        rowCount,
                                        creationTime,
                                        efficiency,
                                        type));
                                
                                logger.info("文件: {}, 大小: {} MB, 行数: {}, 效率: {}",
                                        path,
                                        fileSize / (1024.0 * 1024.0),
                                        rowCount,
                                        efficiency);
                            } catch (Exception e) {
                                logger.warn("获取文件信息失败: {}, 错误: {}", path, e.getMessage());
                            }
                        });
            } else {
                logger.info("类型 {} 的转换后数据目录不存在: {}", type, typePath);
            }
        }
        
        return parquetFiles;
    }
    /**
     * 读取并处理单个Parquet文件
     */
    private Dataset<Row> readAndProcessFile(String path, SparkSession spark) {
        Dataset<Row> df = spark.read()
                .option("mergeSchema", "false")
                .option("encoding", "UTF-8")
                .parquet(path);
        
        // 检查是否包含银行卡号相关字段，如果包含则转换为字符串类型
        String[] cardNumberColumns = {"交易方账户", "对手方账户", "交易流水号", "account_key"};
        for (String column : cardNumberColumns) {
            if (Arrays.asList(df.columns()).contains(column)) {
                df = df.withColumn(column, functions.col(column).cast("string"));
            }
        }
        
        return df;
    }
    /**
     * 处理没有找到Parquet文件的情况
     */
    private void handleEmptyParquetFiles() {
        logger.warn("没有找到交易流水相关的parquet文件，跳过合并");
        boolean hasCSVFiles = false;
        String[] transactionTypes = {"交易流水", "支付宝", "银行", "微信"};
        for (String type : transactionTypes) {
            Path csvPath = Paths.get(classifiedDir, "transformed", type + "_csv");
            if (Files.exists(csvPath)) {
                hasCSVFiles = true;
                logger.info("找到类型 {} 的CSV文件: {}", type, csvPath);
            }
        }
        
        if (hasCSVFiles) {
            logger.info("找到CSV文件但没有parquet文件，尝试从CSV文件读取数据");
        }
    }
    
    /**
     * 处理并合并所有文件
     */
    private Dataset<Row> processAndMergeFiles(SparkSession spark, List<ParquetFileInfo> parquetFiles) {
        Dataset<Row> mergedData = null;
        for (ParquetFileInfo fileInfo : parquetFiles) {
            try {
                Dataset<Row> df = readAndProcessFile(fileInfo.path, spark);
				df = ensureSourceInfoColumn(df, fileInfo.type);
                if (mergedData == null) {
                    mergedData = df;
                } else {
                    // 使用对齐方法合并数据集
                    mergedData = alignAndUnionDatasets(mergedData, df);
                }
            } catch (Exception e) {
                logger.error("读取parquet文件失败: {}, 错误: {}", fileInfo.path, e.getMessage());
            }
        }
        
        return mergedData;
    }
    
    /**
     * 确保数据集包含原始数据信息位置列
     */
    private Dataset<Row> ensureSourceInfoColumn(Dataset<Row> df, String fileType) {
        boolean hasSourceInfo = Arrays.asList(df.columns()).contains("原始数据信息位置");
        logger.info("数据集 {} 是否包含原始数据信息位置列: {}", fileType, hasSourceInfo);
        
        if (!hasSourceInfo) {
            logger.warn("数据集 {} 缺少原始数据信息位置列，尝试创建", fileType);
            // 检查是否有源文件名和行号列
            boolean hasSourceFile = Arrays.asList(df.columns()).contains("源文件名");
            boolean hasFileName = Arrays.asList(df.columns()).contains("文件名");
            boolean hasFilePath = Arrays.asList(df.columns()).contains("文件路径");
            boolean hasRowNum = Arrays.asList(df.columns()).contains("行号");
            
            if (hasSourceFile && hasRowNum) {
                // 如果有源文件名和行号，使用它们创建原始数据信息位置
                df = df.withColumn("原始数据信息位置",
                        functions.concat(
                                functions.lit(fileType + ":"),
                                functions.col("源文件名"),
                                functions.lit(":第"),
                                functions.col("行号").cast("string"),
                                functions.lit("行")
                        )
                );
                logger.info("已为数据集 {} 创建原始数据信息位置列", fileType);
            } else if (hasFileName) {
                // 如果有文件名列但没有源文件名和行号
                df = df.withColumn("原始数据信息位置", 
                    functions.concat(
                        functions.lit(fileType + ":"),
                        functions.col("文件名"),
                        functions.lit(" ("),
                        functions.when(functions.lit(hasFilePath), functions.col("文件路径")).otherwise(functions.lit("")),
                        functions.lit(")")
                    )
                );
                logger.info("已使用文件名为数据集 {} 创建原始数据信息位置列", fileType);
            } else {
                // 如果没有任何文件信息，使用默认值
                df = df.withColumn("原始数据信息位置", 
                    functions.lit(fileType + ":" + (hasFilePath ? df.first().getAs("文件路径") : "未知文件"))
                );
                logger.info("已为数据集 {} 创建默认原始数据信息位置列", fileType);
            }
        }
        return df;
    }
    /**
     * 保存合并后的数据
     */
    private void saveMergedData(Dataset<Row> mergedData, Path transactionOutputPath) {
        try {
            long totalRows = mergedData.count();
            logger.info("所有数据合并完成，共 {} 行", totalRows);
            String mergedOutputDir = transactionOutputPath.toString();
            if (!Arrays.asList(mergedData.columns()).contains("原始数据信息位置")) {
                logger.warn("合并后的数据集缺少原始数据信息位置列，添加默认值");
                mergedData = mergedData.withColumn("原始数据信息位置", functions.lit("未知"));
            }
            // 保存合并后的数据
            mergedData.coalesce(1)
                    .write()
                    .option("header", "true")
                    .option("compression", "snappy")
                    .option("encoding", "UTF-8")
                    .option("charset", "UTF-8")
                    .mode(SaveMode.Overwrite)
                    .parquet(mergedOutputDir);
            
        } catch (Exception e) {
            logger.error("保存合并数据失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public Dataset<Row> alignAndUnionDatasets(Dataset<Row> ds1, Dataset<Row> ds2) {
        try {
            logger.info("开始对齐并合并数据集");
            logger.info("数据集1列: {}", Arrays.toString(ds1.columns()));
            logger.info("数据集2列: {}", Arrays.toString(ds2.columns()));
            Set<String> columns1 = new HashSet<>(Arrays.asList(ds1.columns()));
            Set<String> columns2 = new HashSet<>(Arrays.asList(ds2.columns()));
            Set<String> allColumns = new HashSet<>(columns1);
            allColumns.addAll(columns2);
            // 检查是否存在原始数据信息位置列
            boolean hasSourceInfo1 = columns1.contains("原始数据信息位置");
            boolean hasSourceInfo2 = columns2.contains("原始数据信息位置");
            logger.info("数据集1是否包含原始数据信息位置列: {}", hasSourceInfo1);
            logger.info("数据集2是否包含原始数据信息位置列: {}", hasSourceInfo2);
            
            // 检查是否存在文件路径和文件名列
            boolean hasFilePath1 = columns1.contains("文件路径");
            boolean hasFilePath2 = columns2.contains("文件路径");
            boolean hasFileName1 = columns1.contains("文件名");
            boolean hasFileName2 = columns2.contains("文件名");
            
            // 如果任一数据集缺少原始数据信息位置列，则添加
            if (!hasSourceInfo1) {
                ds1 = ensureSourceInfoColumn(ds1, "数据集1");
            }
            
            if (!hasSourceInfo2) {
                ds2 = ensureSourceInfoColumn(ds2, "数据集2");
            }
            
            // 如果任一数据集缺少文件路径或文件名列，则添加
            if (!hasFilePath1 && hasSourceInfo1) {
                ds1 = ds1.withColumn("文件路径", 
                    functions.regexp_extract(functions.col("原始数据信息位置"), ".*?:([^:]+).*", 1));
            }
            
            if (!hasFilePath2 && hasSourceInfo2) {
                ds2 = ds2.withColumn("文件路径", 
                    functions.regexp_extract(functions.col("原始数据信息位置"), ".*?:([^:]+).*", 1));
            }
            
            if (!hasFileName1 && hasSourceInfo1) {
                ds1 = ds1.withColumn("文件名", 
                    functions.element_at(functions.split(
                        functions.regexp_extract(functions.col("原始数据信息位置"), ".*?:([^:]+).*", 1), 
                        "/"), -1));
            }
            
            if (!hasFileName2 && hasSourceInfo2) {
                ds2 = ds2.withColumn("文件名", 
                    functions.element_at(functions.split(
                        functions.regexp_extract(functions.col("原始数据信息位置"), ".*?:([^:]+).*", 1), 
                        "/"), -1));
            }
            
            // 更新列集合
            columns1 = new HashSet<>(Arrays.asList(ds1.columns()));
            columns2 = new HashSet<>(Arrays.asList(ds2.columns()));
            allColumns = new HashSet<>(columns1);
            allColumns.addAll(columns2);
            // 显示更新后的列信息
            logger.info("更新后数据集1列: {}", Arrays.toString(ds1.columns()));
            logger.info("更新后数据集2列: {}", Arrays.toString(ds2.columns()));
            // 为所有缺失的列添加null值
            ds1 = ensureColumns(ds1, new ArrayList<>(allColumns));
            ds2 = ensureColumns(ds2, new ArrayList<>(allColumns));
            List<String> columnList = new ArrayList<>(allColumns);
            Column[] columnArray = columnList.stream()
                .map(functions::col)
                .toArray(Column[]::new);
            // 确保两个数据集使用相同的列顺序
            ds1 = ds1.select(columnArray);
            ds2 = ds2.select(columnArray);
            // 检查最终结果中是否包含原始数据信息位置列
            boolean finalHasSourceInfo = Arrays.asList(ds1.columns()).contains("原始数据信息位置");
            logger.info("最终合并数据集是否包含原始数据信息位置列: {}", finalHasSourceInfo);
            // 合并数据集
            Dataset<Row> unionedDs = ds1.union(ds2);
            logger.info("数据集合并完成，最终列: {}", Arrays.toString(unionedDs.columns()));
            
            return unionedDs;
        } catch (Exception e) {
            logger.error("对齐并合并数据集失败: {}", e.getMessage(), e);
            throw new RuntimeException("对齐并合并数据集失败", e);
        }
    }

    @Override
    public Dataset<Row> ensureColumns(Dataset<Row> df, List<String> requiredColumns) {
        for (String column : requiredColumns) {
            if (!Arrays.asList(df.columns()).contains(column)) {
                logger.info("在数据集中添加缺失的列: {}", column);
                df = df.withColumn(column, functions.lit(null));
            }
        }
        return df;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    /**
     * Parquet文件信息类，用于存储文件的元数据
     */
    private static class ParquetFileInfo {
        final String path;
        final long size;
        final long rowCount;
        final long creationTime;
        final double efficiency;
        final String type;
        
        ParquetFileInfo(String path, long size, long rowCount, long creationTime, double efficiency, String type) {
            this.path = path;
            this.size = size;
            this.rowCount = rowCount;
            this.creationTime = creationTime;
            this.efficiency = efficiency;
            this.type = type;
        }
    }

    /**
     * 使用银行账户信息丰富银行交易流水数据
     * 将银行账户信息中的交易方名字和账户证件号码通过交易卡号关联到银行交易流水中 (修改：使用交易卡号)
     */
    public void enrichBankTransactionWithAccountInfo() {
        logger.info("开始使用银行账户信息填充银行交易流水数据 (基于交易卡号)");

        try {
            SparkSession spark = getSparkSession();
            setupSparkConfig(spark);
            // 1. 读取银行交易流水数据
            String bankTransactionPath = Paths.get(classifiedDir, "transformed", "银行_交易流水").toString();
            if (!Files.exists(Paths.get(bankTransactionPath))) {
                logger.warn("银行交易流水数据目录不存在: {}", bankTransactionPath);
                return;
            }
            Dataset<Row> bankTransactionData = spark.read()
                    .option("mergeSchema", "false") // Keep mergeSchema false unless explicitly needed and schemas are compatible
                    .option("encoding", "UTF-8")
                    .parquet(bankTransactionPath);

            // 检查并确保交易卡号列存在且为字符串类型
            if (!Arrays.asList(bankTransactionData.columns()).contains("交易卡号")) {
                logger.error("银行交易流水缺少'交易卡号'列，无法进行填充。可用列: {}", String.join(", ", bankTransactionData.columns()));
                // Optional: Attempt to use '交易账号' as a fallback if '交易卡号' is missing?
                // if (Arrays.asList(bankTransactionData.columns()).contains("交易账号")) {
                //     logger.warn("尝试使用'交易账号'代替'交易卡号'进行关联。");
                //     bankTransactionData = bankTransactionData.withColumnRenamed("交易账号", "交易卡号"); // Temporary rename for consistent logic
                // } else {
                //     return; // Exit if neither key column exists
                // }
                 return; // Exit if '交易卡号' is missing
            }
            bankTransactionData = bankTransactionData.withColumn("交易卡号", functions.col("交易卡号").cast("string"));

            logger.info("银行交易流水记录数: {}", bankTransactionData.count());
            if (bankTransactionData.count() > 0) {
                 logger.info("交易流水数据的交易卡号样本:");
                 bankTransactionData.select("交易卡号").filter(functions.col("交易卡号").isNotNull()).distinct().limit(5)
                         .collectAsList().forEach(row -> logger.info("交易流水交易卡号: [{}]", row.get(0)));
            }


            // 2. 读取银行账户信息数据
            String bankAccountPath = Paths.get(classifiedDir, "transformed", "银行_账户信息").toString();
            if (!Files.exists(Paths.get(bankAccountPath))) {
                logger.warn("银行账户信息数据目录不存在: {}", bankAccountPath);
                return;
            }
            Dataset<Row> bankAccountData = spark.read()
                    .option("mergeSchema", "false") // Keep mergeSchema false
                    .option("encoding", "UTF-8")
                    .parquet(bankAccountPath);

            // 检查并确保交易卡号列存在且为字符串类型
            if (!Arrays.asList(bankAccountData.columns()).contains("交易卡号")) {
                logger.error("银行账户信息缺少'交易卡号'列，无法进行填充。可用列: {}", String.join(", ", bankAccountData.columns()));
                // Optional: Fallback logic similar to above if needed
                return;
            }
            bankAccountData = bankAccountData.withColumn("交易卡号", functions.col("交易卡号").cast("string"));

            // 检查姓名和身份证列是否存在，用于填充
            // 根据data_transform.json配置，银行账户信息中应该使用 "账户开户名称" 和 "开户人证件号码"
            boolean hasName = Arrays.asList(bankAccountData.columns()).contains("账户开户名称");
            boolean hasId = Arrays.asList(bankAccountData.columns()).contains("开户人证件号码");
            if (!hasName || !hasId) {
                 logger.warn("银行账户信息缺少'账户开户名称' ({}) 或 '开户人证件号码' ({}) 列，填充可能不完整。", hasName, hasId);
            }

            logger.info("银行账户信息记录数: {}", bankAccountData.count());
             if (bankAccountData.count() > 0) {
                 logger.info("银行账户信息表结构: {}", String.join(", ", bankAccountData.columns()));
                 logger.info("账户信息数据的交易卡号样本:");
                 bankAccountData.select("交易卡号", "账户开户名称", "开户人证件号码")
                         .filter(functions.col("交易卡号").isNotNull()).distinct().limit(10).show(false);
            }


            // 查找是否有与交易流水相同的卡号 (用于调试)
            if (bankTransactionData.count() > 0 && bankAccountData.count() > 0) {
                 String[] sampleCardNumbers = bankTransactionData.select("交易卡号")
                     .filter(functions.col("交易卡号").isNotNull())
                     .distinct()
                     .limit(5)
                     .collectAsList()
                     .stream()
                     .map(row -> row.getString(0))
                     .filter(card -> card != null && !card.isEmpty())
                     .toArray(String[]::new);

                 if (sampleCardNumbers.length > 0) {
                     logger.info("在账户信息中查找交易流水中的卡号样本:");
                     for (String cardNumber : sampleCardNumbers) {
                         logger.info("查找卡号: {}", cardNumber);
                         Dataset<Row> matchingAccounts = bankAccountData.filter(functions.col("交易卡号").equalTo(cardNumber));
                         long count = matchingAccounts.count();
                         logger.info("卡号 {} 在账户信息中找到 {} 条记录", cardNumber, count);
                         if (count > 0) {
                             matchingAccounts.select("交易卡号", "账户开户名称", "开户人证件号码").show(false);
                         }
                     }
                 } else {
                     logger.info("未能在交易流水中找到有效的卡号样本进行查找。");
                 }
            }


            // 3. 使用交易卡号进行关联
            // 创建账户信息的临时视图，包含姓名、身份证和卡号 (account_key)
             // Select only necessary columns and ensure key is not null
             List<Column> accountSelectCols = new ArrayList<>();
             accountSelectCols.add(functions.col("交易卡号").cast("string").alias("account_key"));
             if (hasName) accountSelectCols.add(functions.col("账户开户名称").alias("account_name"));
             if (hasId) accountSelectCols.add(functions.col("开户人证件号码").alias("account_id_number"));

             Dataset<Row> accountInfoSubset = bankAccountData.select(accountSelectCols.toArray(new Column[0]))
                     .filter(functions.col("account_key").isNotNull().and(functions.col("account_key").notEqual(""))) // Filter null or empty keys
                     .dropDuplicates("account_key"); // Drop duplicates based on the card number

             long accountSubsetCount = accountInfoSubset.count();
             logger.info("去重后的有效账户信息记录数 (用于关联): {}", accountSubsetCount);
             if (accountSubsetCount == 0) {
                 logger.warn("没有有效的账户信息可用于关联，跳过填充步骤。");
                  // Directly save the original transaction data as 'filled' data if no enrichment is possible
                  String enrichedPath = Paths.get(classifiedDir, "transformed", "银行_交易流水_填充").toString();
                  bankTransactionData.coalesce(1).write().option("compression", "snappy").option("encoding", "UTF-8").mode(SaveMode.Overwrite).parquet(enrichedPath);
                  logger.info("由于无有效账户信息，已将原始银行交易流水数据直接保存到: {}", enrichedPath);
                  // Also save CSV
                  String csvPath = Paths.get(classifiedDir, "transformed", "银行_交易流水_填充_csv").toString();
                  bankTransactionData.coalesce(1).write().option("header", "true").option("encoding", "UTF-8").option("charset", "UTF-8").mode(SaveMode.Overwrite).csv(csvPath);
                  logger.info("已将原始银行交易流水CSV数据保存到: {}", csvPath);
                 return;
             }
             accountInfoSubset.cache(); // Cache the subset for potentially faster joins

            // 注册 UDFs (clean_card_number)
            spark.udf().register(
                    "clean_card_number",
                    (String cardNumber) -> {
                        if (cardNumber == null) return null;
                        // Keep only digits
                        String cleaned = cardNumber.replaceAll("[^0-9]", "");
                        return cleaned.isEmpty() ? null : cleaned; // Return null if cleaning results in empty string
                    },
                    org.apache.spark.sql.types.DataTypes.StringType
            );
             // Note: normalize_card_number UDF is registered later, assuming TransformFunction handles it.

            // 创建用于 join 的清理后的卡号列
            bankTransactionData = bankTransactionData
                .withColumn("clean_card_number", functions.callUDF("clean_card_number", functions.col("交易卡号")))
                 .filter(functions.col("clean_card_number").isNotNull()); // Filter out rows where card number becomes null after cleaning

             accountInfoSubset = accountInfoSubset
                .withColumn("clean_key", functions.callUDF("clean_card_number", functions.col("account_key")))
                 .filter(functions.col("clean_key").isNotNull()); // Filter out rows where account key becomes null after cleaning

            logger.info("清理后的卡号对比样本:");
             if (bankTransactionData.count() > 0) {
                 logger.info("交易流水的清理后卡号 (非空):");
                 bankTransactionData.select("交易卡号", "clean_card_number").distinct().limit(5).show(false);
             }
             if (accountInfoSubset.count() > 0) {
                 logger.info("账户信息的清理后卡号 (非空):");
                 accountInfoSubset.select("account_key", "clean_key").distinct().limit(5).show(false);
             }

            logger.info("尝试多种join方式 (基于清理后的卡号)");
            // 主要使用清理后的卡号进行精确匹配
            Dataset<Row> enrichedData = bankTransactionData.join(
                    functions.broadcast(accountInfoSubset), // Broadcast the smaller account subset
                    bankTransactionData.col("clean_card_number").equalTo(accountInfoSubset.col("clean_key")),
                    "left_outer"
            );

            long initialMatchCount = enrichedData.filter(functions.col("account_key").isNotNull()).count();
            long totalTransactionRows = bankTransactionData.count(); // Count after filtering invalid cleaned numbers
            logger.info("主要匹配结果 (清理后卡号精确匹配): 总有效交易行数 {}, 匹配成功 {}, 成功率 {}%",
                totalTransactionRows, initialMatchCount, totalTransactionRows == 0 ? 0 : String.format("%.2f", (double)initialMatchCount/totalTransactionRows*100));


            // --- Optional: Add fallback join strategies if needed, e.g., raw card number match ---
            // if (initialMatchCount / (double) totalTransactionRows < 0.8) { // Example threshold
            //     logger.warn("Initial match rate is low, attempting fallback join on raw card numbers.");
            //     Dataset<Row> fallbackJoin = bankTransactionData.join(
            //         functions.broadcast(accountInfoSubset),
            //         bankTransactionData.col("交易卡号").equalTo(accountInfoSubset.col("account_key")), // Join on raw card number
            //         "left_outer"
            //     );
            //     // Logic to combine results or choose the best one
            // }
             // --- Optional: Last N digits match ---
             // bankTransactionData = bankTransactionData.withColumn("card_number_last_digits", functions.expr("substring(clean_card_number, -10, 10)"));
             // accountInfoSubset = accountInfoSubset.withColumn("key_last_digits", functions.expr("substring(clean_key, -10, 10)"));
             // Dataset<Row> enrichedData3 = bankTransactionData...join... on last digits ...


            // 4. 填充交易方户名和交易方身份证
             if (hasName) {
                 logger.info("使用 '账户开户名称' 填充 '交易方户名' 列");
                  enrichedData = enrichedData.withColumn("交易方户名_filled",
                      functions.when(
                          functions.col("account_name").isNotNull(),
                          functions.col("account_name")
                     ).otherwise(functions.col("交易方户名")));
                  enrichedData = enrichedData.drop("交易方户名").withColumnRenamed("交易方户名_filled", "交易方户名");
             } else {
                 logger.warn("账户信息中无 '账户开户名称' 列，跳过填充。");
             }

             if (hasId) {
                  logger.info("使用 '开户人证件号码' 填充 '交易方证件号码' 列");
                   enrichedData = enrichedData.withColumn("交易方证件号码_filled",
                       functions.when(
                           functions.col("account_id_number").isNotNull(),
                           functions.col("account_id_number")
                      ).otherwise(functions.col("交易方证件号码")));
                    enrichedData = enrichedData.drop("交易方证件号码").withColumnRenamed("交易方证件号码_filled", "交易方证件号码");
             } else {
                 logger.warn("账户信息中无 '开户人证件号码' 列，跳过填充。");
             }


            // 调试：显示几条可能被填充的数据
            logger.info("显示几条可能被填充的数据:");
            enrichedData.filter(functions.col("account_key").isNotNull()) // Show rows where a match occurred
                .select("交易卡号", "交易方户名", "交易方证件号码", "account_name", "account_id_number")
                .limit(10)
                .show(false);
                 
            // 展示填充前后对比
            logger.info("填充前后对比样本:");
            Dataset<Row> sampleBefore = bankTransactionData.select("交易卡号", "交易方户名", "交易方证件号码")
                .limit(5);
            Dataset<Row> sampleAfter = enrichedData.select("交易卡号", "交易方户名", "交易方证件号码")
                .limit(5);
            logger.info("填充前:");
            sampleBefore.show(false);
            logger.info("填充后:");
            sampleAfter.show(false);


            // 5. 删除所有临时列
            List<String> columnsToDrop = new ArrayList<>(Arrays.asList("account_name", "account_id_number", "account_key",
                "clean_card_number", "clean_key")); // Add any other temp cols like *_last_digits if used
            for (String col : columnsToDrop) {
                 if (Arrays.asList(enrichedData.columns()).contains(col)) {
                     enrichedData = enrichedData.drop(col);
                 }
            }
            logger.debug("最终数据集列: {}", String.join(", ", enrichedData.columns()));

            // 6. 保存填充后的数据
            String enrichedPath = Paths.get(classifiedDir, "transformed", "银行_交易流水_填充").toString();
            enrichedData.coalesce(1) // Coalesce to 1 partition for single file output
                    .write()
                    .option("compression", "snappy")
                    .option("encoding", "UTF-8")
                    .mode(SaveMode.Overwrite)
                    .parquet(enrichedPath);
            logger.info("已将填充后的银行交易流水数据保存到 Parquet: {}", enrichedPath);

            // 7. 同时保存CSV格式 (可选，用于调试)
            String csvPath = Paths.get(classifiedDir, "transformed", "银行_交易流水_填充_csv").toString();
            enrichedData.coalesce(1)
                    .write()
                    .option("header", "true")
                    .option("encoding", "GBK") // Try GBK for Excel compatibility if UTF-8 causes issues
                    //.option("encoding", "UTF-8") // Standard UTF-8
                    .option("charset", "GBK")   // Or UTF-8
                    .option("escape", "\"")      // Ensure quotes are escaped correctly
                    .mode(SaveMode.Overwrite)
                    .csv(csvPath);
            logger.info("已将填充后的银行交易流水CSV数据保存到 CSV: {}", csvPath);

            accountInfoSubset.unpersist(); // Unpersist the cached DataFrame

        } catch (Exception e) {
            logger.error("使用银行账户信息填充交易流水数据失败 (基于交易卡号): {}", e.getMessage(), e);
            // Consider re-throwing or handling the exception appropriately
        }
    }

    /**
     * 数据库统计信息记录
     */
    private void logDatabaseStatistics() {
        logger.info("=== 数据库存储统计信息 ===");
        try {
            String[] tableTypes = {"transaction", "personnel", "task", "account"};
            String[] tableNames = {"交易流水", "人员信息", "任务信息", "账户信息"};
            
            for (int i = 0; i < tableTypes.length; i++) {
                long totalCount = databaseService.getRecordCount(tableTypes[i], null);
                logger.info("{} 表总记录数: {}", tableNames[i], totalCount);
                
                // 按数据源统计
                if (totalCount > 0) {
                    logger.info("  - 数据源分布:");
                    // 这里可以进一步细化统计逻辑
                }
            }
            
            // 检查数据库连接状态
            boolean dbStatus = databaseService.checkDatabaseConnection();
            logger.info("数据库连接状态: {}", dbStatus ? "正常" : "异常");
            
        } catch (Exception e) {
            logger.error("获取数据库统计信息失败: {}", e.getMessage(), e);
        }
        logger.info("=== 数据库统计信息记录完成 ===");
    }

    /**
     * 导出结果到 Excel 文件（已废弃，改为数据库存储）
     */
    @Deprecated
    private void exportResultsToExcel() {
        logger.info("开始导出结果到 Excel 文件...");
        SparkSession spark = getSparkSession();
        String excelBaseDir = Paths.get(outputDir, "excel_exports").toString();

        try {
            // 确保输出目录存在
            Files.createDirectories(Paths.get(excelBaseDir));

            // 1. 导出交易流水信息
            exportSingleExcel(spark,
                    Paths.get(outputDir, "交易流水").toString(), // 读取合并后的交易流水 Parquet
                    Paths.get(excelBaseDir, "交易流水信息.xlsx").toString(),
                    "交易流水信息",
                    Arrays.asList(
                        "交易卡号", "交易账号", "交易方户名", "交易方证件号码", "交易时间", "交易金额",
                        "交易余额", "收付标志", "交易对手账卡号", "现金标志", "对手户名", "对手身份证号",
                        "对手开户银行", "摘要说明", "交易币种", "交易网点名称", "交易发生地",
                        "交易是否成功", "传票号", "IP地址", "MAC地址", "对手交易余额", "交易流水号",
                        "日志号", "凭证种类", "凭证号", "交易柜员号", "备注", "商户名称", "交易类型",
                        "查询反馈结果原因", "开户行", "文件路径" // 确保包含文件路径
                    )
            );

            // 2. 导出卡号信息 (账户信息)
             // 确定账户信息的 Parquet 路径
             String accountInfoPath = Paths.get(classifiedDir, "transformed", "银行_账户信息").toString();
             if (!Files.exists(Paths.get(accountInfoPath))) {
                 logger.warn("未找到银行账户信息 Parquet 文件: {}, 跳过卡号信息 Excel 导出", accountInfoPath);
             } else {
                exportSingleExcel(spark,
                        accountInfoPath,
                        Paths.get(excelBaseDir, "卡号信息.xlsx").toString(),
                        "卡号信息",
                        Arrays.asList(
                            "账户开户名称", "开户人证件号码", "交易卡号", "交易账号", "账号开户时间",
                            "账户余额", "可用余额", "币种", "开户网点代码", "开户网点", "账户状态",
                            "钞汇标志名称", "开户人证件类型", "销户日期", "账户类型", "开户联系方式",
                            "通信地址", "联系电话", "代理人", "代理人电话", "备注", "开户省份",
                            "开户城市", "账号开户银行", "客户代码", "法人代表", "客户工商执照号码",
                            "法人代表证件号码", "住宅地址", "邮政编码", "代办人证件号码", "邮箱地址",
                            "关联资金账户", "地税纳税号", "单位电话", "代办人证件类型", "住宅电话",
                            "法人代表证件类型", "国税纳税号", "单位地址", "工作单位", "销户网点",
                            "最后交易时间", "账户销户银行", "任务流水号", "文件名", "文件路径" // 确保包含文件名和文件路径
                        )
                );
             }

            // 3. 导出人员信息
             // 确定人员信息的 Parquet 路径 (需要确认实际保存的路径)
             String personnelInfoPath = Paths.get(classifiedDir, "transformed", "人员信息").toString(); // 假设路径
             // 尝试备用路径，如果上面的不存在
             if (!Files.exists(Paths.get(personnelInfoPath))) {
                 personnelInfoPath = Paths.get(classifiedDir, "transformed", "银行_人员信息").toString();
             }

             if (!Files.exists(Paths.get(personnelInfoPath))) {
                 logger.warn("未找到人员信息 Parquet 文件: {}, 跳过人员信息 Excel 导出", personnelInfoPath);
             } else {
                exportSingleExcel(spark,
                        personnelInfoPath,
                        Paths.get(excelBaseDir, "人员信息Excel.xlsx").toString(),
                        "人员信息",
                        Arrays.asList(
                            "客户名称", "证照类型", "证照号码", "单位地址", "联系电话", "联系手机",
                            "单位电话", "住宅电话", "工作单位", "邮箱地址", "代办人姓名",
                            "代办人证件类型", "代办人证件号码", "国税纳税号", "地税纳税号",
                            "法人代表", "法人代表证件类型", "法人代表证件号码", "出生日期",
                            "户籍地址", "客户工商执照号码", "文件名", "文件路径" // 确保包含文件名和文件路径
                        )
                );
            }
            String taskInfoPath = Paths.get(classifiedDir,"transformed","任务信息")
                    .toString();
             if(!Files.exists(Paths.get(taskInfoPath))){
                 logger.warn("未找到任务信息 Parquet 文件: {}, 跳过任务信息 Excel 导出", taskInfoPath);
             }else{
                 exportSingleExcel(spark,
                         taskInfoPath,
                         Paths.get(excelBaseDir, "任务信息.xlsx").toString(),
                         "任务信息",
                         Arrays.asList(
                                 "任务流水号", "银行名称", "主体类别", "证帐号码", "发送时间",
                                 "反馈时间", "反馈结果", "反馈非明细结果", "反馈明细结果",
                                 "入库时间", "入库状态", "请求单号", "查询结果"
                         )
                 );
             }
        } catch (Exception e) {
            logger.error("导出结果到 Excel 时发生错误: {}", e.getMessage(), e);
        }
    }

    // --- 新增辅助方法：导出单个 Excel 文件 ---
    private void exportSingleExcel(SparkSession spark, String sourceParquetPath, String targetExcelPath, String dataType, List<String> targetHeaders) {
        logger.info("开始导出 {} 数据到 Excel: {}", dataType, targetExcelPath);
        try {
            Dataset<Row> sourceData = spark.read()
                                         .option("mergeSchema", "false") // 避免合并可能冲突的模式
                                         .parquet(sourceParquetPath);

            long rowCount = sourceData.count();
            logger.info("{} 数据共 {} 行", dataType, rowCount);

            // 选择并排序所需的列
            List<Column> selectedColumns = new ArrayList<>();
            List<String> existingColumns = Arrays.asList(sourceData.columns());

            for (String header : targetHeaders) {
                if (existingColumns.contains(header)) {
                    selectedColumns.add(functions.col(header));
                } else {
                    logger.warn("在 {} 数据中未找到列: '{}'，将添加为空列。", dataType, header);
                    selectedColumns.add(functions.lit("").cast("string").as(header)); // 添加空列并指定名称
                }
            }

            Dataset<Row> dataForExcel = sourceData.select(selectedColumns.toArray(new Column[0]));

            // 限制行数以防 Excel 文件过大 (例如，限制为 1048575 行，Excel 最大行数减1)
            long excelMaxRows = 1048575;
             if (rowCount > excelMaxRows) {
                 logger.warn("{} 数据量 ({}) 超过 Excel 最大行数限制 ({})，将只导出前 {} 行。", dataType, rowCount, excelMaxRows, excelMaxRows);
                 dataForExcel = dataForExcel.limit((int)excelMaxRows);
             }


            // 写入 Excel
            dataForExcel.coalesce(1) // 合并为单个文件
                  .write()
                  .format("com.crealytics.spark.excel")
                  .option("header", "true") // 包含表头
                  .option("dataAddress", "'Sheet1'!A1") // 指定写入位置
                  .option("dateFormat", "yyyy-MM-dd HH:mm:ss") // 日期格式
                  .option("timestampFormat", "yyyy-MM-dd HH:mm:ss") // 时间戳格式
                  .mode(SaveMode.Overwrite) // 覆盖已存在的文件
                  .save(targetExcelPath);

            logger.info("{} Excel 文件保存成功: {}", dataType, targetExcelPath);

        } catch (Exception e) {
            logger.error("导出 {} 数据到 Excel 失败: {}", dataType, e.getMessage(), e);
            // 可以考虑在这里添加备选的 CSV 导出逻辑
             // logger.info("尝试保存 {} 为 CSV 文件作为备选...", dataType);
             // saveAsCsvAlternative(spark, sourceParquetPath, targetExcelPath.replace(".xlsx", ".csv"), dataType, targetHeaders);
        }
    }

    /**
     * 在应用程序关闭时关闭SparkSession
     */
    @PreDestroy
    public void cleanup() {
        logger.info("应用程序关闭中，正在停止SparkSession...");
        if (sparkSession != null) {
            try {
                // 停止SparkSession
                if (!sparkSession.sparkContext().isStopped()) {
                    // 确保Spark UI也会被关闭
                    String uiUrl = sparkSession.sparkContext().uiWebUrl().isDefined() ? 
                        sparkSession.sparkContext().uiWebUrl().get() : null;
                    if (uiUrl != null) {
                        logger.info("关闭Spark UI: {}", uiUrl);
                    }
                    // 使用强制关闭方式
                    logger.info("正在关闭Spark上下文...");
                    sparkSession.sparkContext().stop();
                    sparkSession.close();
                    sparkSession = null;
                    // 手动触发垃圾回收
                    System.gc();
                    logger.info("SparkSession已成功停止");
                }
            } catch (Exception e) {
                logger.error("停止SparkSession时发生错误: {}", e.getMessage(), e);
                // 即使出错也尝试强制清理
                try {
                    if (sparkSession != null && !sparkSession.sparkContext().isStopped()) {
                        logger.info("尝试强制停止SparkContext...");
                        sparkSession.sparkContext().stop();
                    }
                } catch (Exception ex) {
                    logger.error("强制停止SparkContext时发生错误: {}", ex.getMessage(), ex);
                } finally {
                    // 确保引用被清除
                    sparkSession = null;
                    logger.info("SparkSession引用已被清除");
                }
            }
        }
    }
} 