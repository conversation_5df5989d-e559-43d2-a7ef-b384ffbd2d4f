package org.example.xiaowudemo.service;

import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.io.File;
import java.util.List;
import java.util.Map;

public interface SparkService {
    /**
     * 获取配置好的SparkSession实例
     * @return SparkSession实例
     */
    SparkSession getSparkSession();
    
    /**
     * 根据文件类型转换文件
     * @param classifiedFiles 分类后的文件映射
     */
    void transformFiles(Map<String, List<File>> classifiedFiles);
    
    /**
     * 合并交易流水数据
     */
    void mergeTransactionData();
    
    /**
     * 对齐并合并两个数据集
     * @param ds1 第一个数据集
     * @param ds2 第二个数据集
     * @return 合并后的数据集
     */
    Dataset<Row> alignAndUnionDatasets(Dataset<Row> ds1, Dataset<Row> ds2);
    
    /**
     * 确保数据集包含指定的列
     * @param df 数据集
     * @param requiredColumns 必需的列列表
     * @return 处理后的数据集
     */
    Dataset<Row> ensureColumns(Dataset<Row> df, List<String> requiredColumns);
} 