package org.example.xiaowudemo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.Set;

@SpringBootApplication
public class XiaowuDemoApplication {
    private static final Logger logger = LoggerFactory.getLogger(XiaowuDemoApplication.class);

    public static void main(String[] args) {
        // 将命令行参数传递给Spring Boot应用
        ConfigurableApplicationContext context = SpringApplication.run(XiaowuDemoApplication.class, args);
        context.addApplicationListener((ApplicationListener<ApplicationReadyEvent>) event -> startProcessingCompletionChecker(context));
        
        // 添加JVM关闭钩子，确保在任何情况下都能优雅退出
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("JVM关闭钩子被触发，准备关闭应用程序...");
            try {
                // 如果上下文仍然活跃，则关闭它
                if (context.isActive()) {
                    logger.info("关闭Spring上下文...");
                    context.close();
                }
            } catch (Exception e) {
                logger.error("关闭Spring上下文时发生错误", e);
            }
            logger.info("JVM关闭钩子执行完毕");
        }));
    }

    /**
     * 启动一个定时任务，检查处理是否完成，如果完成则等待2秒后退出应用程序
     */
    private static void startProcessingCompletionChecker(ConfigurableApplicationContext context) {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        // 设置为守护线程，避免阻止JVM退出
        ((java.util.concurrent.ThreadPoolExecutor) scheduler).setThreadFactory(r -> {
            Thread t = new Thread(r, "processing-completion-checker");
            t.setDaemon(true);
            return t;
        });
        
        // 每秒检查一次处理完成标志
        scheduler.scheduleAtFixedRate(() -> {
            String isComplete = System.getProperty("app.processing.complete");
            if ("true".equals(isComplete)) {
                logger.info("检测到处理完成标志，准备在2秒后退出应用程序...");
                // 关闭定时任务
                scheduler.shutdown();
                // 等待2秒后退出
                try {
                    Thread.sleep(2000);
                    logger.info("应用程序正在退出...");
                    
                    // 获取所有非守护线程
                    logger.info("检查并终止所有非守护线程...");
                    Set<Thread> threadSet = Thread.getAllStackTraces().keySet();
                    for (Thread t : threadSet) {
                        if (!t.isDaemon() && !t.equals(Thread.currentThread()) && t.isAlive()) {
                            logger.info("发现活动的非守护线程: {}，尝试中断", t.getName());
                            t.interrupt();
                        }
                    }
                    
                    // 强制退出JVM
                    logger.info("强制退出应用程序");
                    Runtime.getRuntime().halt(0); // 使用halt而不是exit，确保立即终止JVM
                } catch (InterruptedException e) {
                    logger.error("等待退出时被中断", e);
                    Thread.currentThread().interrupt();
                    // 即使被中断也强制退出
                    Runtime.getRuntime().halt(1);
                }
            }
        }, 1, 1, TimeUnit.SECONDS);
        
        logger.info("处理完成检查器已启动");
    }
}
