package org.example.xiaowudemo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 数据库配置类
 * 用于读取数据库相关的配置参数
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "database")
public class DatabaseConfig {
    private Batch batch = new Batch();
    private Connection connection = new Connection();
    private Performance performance = new Performance();

    /**
     * 控制批处理数据操作的性能参数
     */
    @Data
    public static class Batch {
        private int size = 1000;
        private int timeout = 30;
    }

    /**
     * 管理数据库连接的参数，添加重试逻辑
     */
    @Data
    public static class Connection {
        private int maxRetry = 3;
        private long retryDelay = 1000;
    }

    /**
     * 控制数据库操作的性能优化策略
     */
    @Data
    public static class Performance {
        private boolean enableBatchInsert = true;
        private boolean enableParallelProcessing = true;
        private int maxParallelThreads = 4;
    }

}