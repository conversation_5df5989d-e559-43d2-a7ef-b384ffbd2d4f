package org.example.xiaowudemo.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 数据库初始化器
 * 在应用启动时检查并创建必要的数据库表结构
 */
@Component
public class DatabaseInitializer implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseInitializer.class);

    private final JdbcTemplate jdbcTemplate;
    
    public DatabaseInitializer(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public void run(String... args) throws Exception {
        logger.info("开始检查数据库表结构...");
        
        try {
            // 检查主要表是否存在
            if (!tableExists("交易明细")) {
                logger.info("检测到数据库表不存在，开始执行初始化脚本...");
                executeSqlScript();
            } else {
                logger.info("数据库表已存在，跳过初始化");
            }
            // 验证表结构
            validateTables();
        } catch (Exception e) {
            logger.error("数据库初始化失败: {}", e.getMessage(), e);
            // 不抛出异常，让应用继续启动，但记录错误
        }
    }

    /**
     * 检查表是否存在
     */
    private boolean tableExists(String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables " +
                        "WHERE table_schema = DATABASE() AND table_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
            return count > 0;
        } catch (Exception e) {
            logger.warn("检查表 {} 是否存在时出错: {}", tableName, e.getMessage());
            return false;
        }
    }

    /**
     * 执行SQL脚本文件
     */
    private void executeSqlScript() {
        try {
            ClassPathResource resource = new ClassPathResource("database/schema.sql");
            if (!resource.exists()) {
                logger.warn("SQL脚本文件不存在: database/schema.sql");
                return;
            }
            StringBuilder sqlScript = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // 跳过注释和空行
                    line = line.trim();
                    if (!line.isEmpty() && !line.startsWith("--") && !line.startsWith("#")) {
                        sqlScript.append(line).append("\n");
                    }
                }
            }
            // 按分号分割并执行SQL语句
            String[] sqlStatements = sqlScript.toString().split(";");
            for (String sql : sqlStatements) {
                sql = sql.trim();
                if (!sql.isEmpty()) {
                    String arg1 = sql.substring(0, Math.min(sql.length(), 50)) + "...";
                    try {
                        jdbcTemplate.execute(sql);
                        logger.debug("执行SQL: {}", arg1);
                    } catch (Exception e) {
                        logger.warn("执行SQL语句失败: {}, 错误: {}",
                                arg1, e.getMessage());
                    }
                }
            }
            logger.info("数据库初始化脚本执行完成");
        } catch (Exception e) {
            logger.error("执行数据库初始化脚本失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 验证关键表结构
     */
    private void validateTables() {
        String[] requiredTables = {
            "交易明细", "人员信息", "任务信息", "卡号信息"
        };

        logger.info("验证数据库表结构...");
        
        for (String tableName : requiredTables) {
            try {
                if (tableExists(tableName)) {
                    // 获取表的记录数 - 使用反引号包围中文表名
                    String countSql = "SELECT COUNT(*) FROM `" + tableName + "`";
                    Long count = jdbcTemplate.queryForObject(countSql, Long.class);
                    logger.info("表 {} 验证通过，当前记录数: {}", tableName, count);
                } else {
                    logger.warn("表 {} 不存在", tableName);
                }
            } catch (Exception e) {
                logger.error("验证表 {} 失败: {}", tableName, e.getMessage());
            }
        }

        logger.info("数据库表结构验证完成");
    }
}