<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.0</version>
        <relativePath/>
    </parent>

    <groupId>org.example</groupId>
    <artifactId>xiaowudemo</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>xiaowudemo</name>
    <description>Demo project for Spring Boot</description>

    <properties>
        <java.version>21</java.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <slf4j.version>2.0.9</slf4j.version>
        <logback.version>1.4.14</logback.version>
        <spark.version>3.5.6</spark.version>
        <scala.version>2.12</scala.version>
        <scala.binary.version>2.12.18</scala.binary.version>
        <jakarta-servlet.version>4.0.3</jakarta-servlet.version>
        <jersey.version>2.36</jersey.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>6.1.1</version>
        </dependency>
        <!-- 添加 spring-boot-starter-web 依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>

        <!-- Scala -->
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <version>${scala.binary.version}</version>
        </dependency>

        <!-- Spark Dependencies -->
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-core_${scala.version}</artifactId>
            <version>${spark.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.reload4j</groupId>
                    <artifactId>reload4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-sql_${scala.version}</artifactId>
            <version>${spark.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-yarn_${scala.version}</artifactId>
            <version>${spark.version}</version>
        </dependency>

        <!-- Spark Excel with careful exclusions -->
        <dependency>
            <groupId>com.crealytics</groupId>
            <artifactId>spark-excel_2.12</artifactId>
            <version>0.14.0</version>
        </dependency>

        <!-- Database -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.28</version>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        
        <!-- Spring JDBC Template instead of JPA/Hibernate -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        
        <!-- HikariCP connection pool -->
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        
        <!-- Bean Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Other Dependencies -->
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>5.0.0</version>
            <scope>compile</scope>
        </dependency>

        <!-- 添加 Javax Servlet API -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>4.0.1</version>
            <scope>compile</scope>
        </dependency>

        <!-- 添加 Jersey Servlet 容器 -->
        <dependency>
            <groupId>org.glassfish.jersey.containers</groupId>
            <artifactId>jersey-container-servlet</artifactId>
            <version>3.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>minlog</artifactId>
            <version>1.3.0</version>
        </dependency>
        <!-- 添加 Jersey 注入支持 -->
        <dependency>
            <groupId>org.glassfish.jersey.inject</groupId>
            <artifactId>jersey-hk2</artifactId>
            <version>3.1.3</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-common</artifactId>
            <version>3.1.3</version>
        </dependency>
        <!-- Gson for JSON processing -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>



        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- 在dependencies节点下添加 -->
        <dependency>
            <groupId>io.delta</groupId>
            <artifactId>delta-core_2.12</artifactId>
            <version>2.4.0</version>
        </dependency>

        <!-- 添加Hadoop Windows工具依赖 -->
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <version>3.3.4</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 添加LZ4压缩支持 -->
        <dependency>
            <groupId>org.lz4</groupId>
            <artifactId>lz4-java</artifactId>
            <version>1.8.0</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.2</version>
        </dependency>

        <!-- 添加log4j-core以支持Spark，但排除SLF4J绑定以避免冲突 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.20.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.20.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-cli</groupId>
            <artifactId>commons-cli</artifactId>
            <version>1.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        
        <!-- JUniversalChardet 编码检测库 -->
        <dependency>
            <groupId>com.googlecode.juniversalchardet</groupId>
            <artifactId>juniversalchardet</artifactId>
            <version>1.0.3</version>
        </dependency>
        
        <!-- OkHttp for HTTP client -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.12.0</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.juniversalchardet</groupId>
                <artifactId>juniversalchardet</artifactId>
                <version>1.0.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <jvmArguments>
                        --add-exports=java.base/sun.util.calendar=ALL-UNNAMED
                        --add-opens=java.base/sun.nio.ch=ALL-UNNAMED
                        --add-opens=java.base/java.lang=ALL-UNNAMED
                        --add-opens=java.base/java.util=ALL-UNNAMED
                        --add-opens=java.base/java.nio=ALL-UNNAMED
                        --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
                        --add-opens=java.base/java.text=ALL-UNNAMED
                        --add-opens=java.desktop/java.awt.font=ALL-UNNAMED
                        --add-exports=java.base/sun.security.action=ALL-UNNAMED
                        --add-exports=java.base/sun.nio.ch=ALL-UNNAMED
                        --add-exports=java.base/sun.reflect=ALL-UNNAMED
                        --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
                        --illegal-access=permit
                    </jvmArguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <descriptorRefs>
                        <descriptorRef>jar-with-dependencies</descriptorRef>
                    </descriptorRefs>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <mainClass>org.example.xiaowudemo.cli.DataProcessorCLI</mainClass>
                        </manifest>
                        <manifestEntries>
                            <Multi-Release>true</Multi-Release>
                            <Class-Path>.</Class-Path>
                        </manifestEntries>
                    </archive>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            
            <!-- 添加launch4j插件，用于打包为Windows可执行文件 -->
            <plugin>
                <groupId>com.akathist.maven.plugins.launch4j</groupId>
                <artifactId>launch4j-maven-plugin</artifactId>
                <version>2.4.1</version>
                <executions>
                    <execution>
                        <id>l4j-clui</id>
                        <phase>package</phase>
                        <goals>
                            <goal>launch4j</goal>
                        </goals>
                        <configuration>
                            <headerType>console</headerType>
                            <outfile>target/CleanBankTool.exe</outfile>
                            <jar>target/${project.artifactId}-${project.version}-jar-with-dependencies.jar</jar>
                            <errTitle>清洗工具错误</errTitle>
                            <classPath>
                                <mainClass>org.example.xiaowudemo.XiaowuDemoApplication</mainClass>
                                <addDependencies>false</addDependencies>
                                <preCp>anything</preCp>
                            </classPath>
                            <cmdLine>%1</cmdLine>
                            <jre>
                                <path>./jre</path>
                                <requires64Bit>true</requires64Bit>
                                <minVersion>21</minVersion>
                                <initialHeapSize>512</initialHeapSize>
                                <maxHeapSize>4096</maxHeapSize>
                                <opts>
                                    <opt>-Dfile.encoding=GB2312</opt>
                                    <opt>-XX:+UseG1GC</opt>
                                    <opt>-XX:+UseCompressedOops</opt>
                                    <opt>-XX:G1HeapRegionSize=32m</opt>
                                    <opt>-XX:+DisableExplicitGC</opt>
                                    <opt>-XX:+AlwaysPreTouch</opt>
                                    <opt>-Dspark.driver.memory=4g</opt>
                                    <opt>-Dspark.executor.memory=4g</opt>
                                    <opt>-Dspark.executor.cores=4</opt>
                                    <opt>-Dspark.executor.instances=2</opt>
                                    <opt>-Dspark.sql.adaptive.enabled=true</opt>
                                    <opt>-Dspark.driver.userClassPathFirst=true</opt>
                                    <opt>-Dspark.sql.adaptive.coalescePartitions.enabled=true</opt>
                                    <opt>-Dspark.memory.fraction=0.8</opt>
                                    <opt>-Dspark.memory.storageFraction=0.3</opt>
                                    <opt>-Dspark.local.dir=./tmp</opt>
                                    <opt>-Dspark.sql.files.maxPartitionBytes=134217728</opt>
                                    <opt>--add-opens=java.base/java.lang=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/java.lang.invoke=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/java.lang.reflect=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/java.io=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/java.net=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/java.nio=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/java.util=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/java.util.concurrent=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/sun.nio.ch=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/sun.nio.cs=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/sun.security.action=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.base/sun.util.calendar=ALL-UNNAMED</opt>
                                    <opt>--add-exports=java.base/sun.nio.ch=ALL-UNNAMED</opt>
                                    <opt>--add-opens=java.management/sun.management=ALL-UNNAMED</opt>
                                    <opt>--add-opens=jdk.management/com.sun.management.internal=ALL-UNNAMED</opt>
                                </opts>
                            </jre>
                            <versionInfo>
                                <fileVersion>*******</fileVersion>
                                <txtFileVersion>${project.version}</txtFileVersion>
                                <fileDescription>银行数据清洗工具</fileDescription>
                                <copyright>Copyright (c) 2025</copyright>
                                <productVersion>*******</productVersion>
                                <txtProductVersion>${project.version}</txtProductVersion>
                                <productName>银行数据清洗工具</productName>
                                <internalName>CleanBankTool</internalName>
                                <originalFilename>CleanBankTool.exe</originalFilename>
                            </versionInfo>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <name>Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
        </repository>
        <repository>
            <id>aliyun</id>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
        <repository>
            <id>spark-packages</id>
            <url>https://repos.spark-packages.org/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>aliyun-plugin</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>