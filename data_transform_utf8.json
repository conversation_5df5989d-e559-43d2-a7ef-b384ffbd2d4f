{"fileTypes": [{"name": "银行", "OutputDir": "银行", "keyWords": ["交易方户名", "交易卡号", "交易网点名称", "交易方证件号码", "对手户名", "交易对手账卡号", "对手开户银行", "对手身份证号", "交易流水号", "交易时间", "交易金额", "交易余额", "收付标志", "摘要说明", "交易币种", "交易发生地", "交易是否成功", "传票号", "日志号", "凭证种类", "凭证号", "交易柜员号", "商户名称", "交易类型", "账户开户名称", "开户人证件号码", "账号开户时间", "账户余额", "可用余额", "开户网点", "账户状态", "销户日期", "账户类型", "现金标志", "IP地址", "MAC地址", "对手交易余额", "查询反馈结果原因", "开户行", "客户名称", "证照类型", "证照号码", "单位地址", "联系电话", "联系手机", "单位电话", "住宅电话", "工作单位", "邮箱地址", "代办人姓名", "代办人证件类型", "代办人证件号码", "国税纳税号", "地税纳税号", "法人代表", "法人代表证件类型", "法人代表证件号码", "出生日期", "户籍地址", "客户工商执照号码"], "subTypes": [{"name": "交易流水", "keyWords": ["交易方户名", "交易卡号", "交易网点名称", "交易方证件号码", "对手户名", "交易对手账卡号", "对手开户银行", "对手身份证号", "交易流水号", "交易时间", "交易金额", "交易余额", "收付标志", "摘要说明", "交易币种", "交易发生地", "交易是否成功", "传票号", "日志号", "凭证种类", "凭证号", "交易柜员号", "商户名称", "交易类型", "现金标志", "IP地址", "MAC地址", "对手交易余额", "查询反馈结果原因", "开户行"]}, {"name": "账户信息", "keyWords": ["账户开户名称", "开户人证件号码", "交易卡号", "交易账号", "账号开户时间", "账户余额", "可用余额", "币种", "开户网点代码", "开户网点", "账户状态", "钞汇标志名称", "开户人证件类型", "销户日期", "账户类型", "开户联系方式", "通信地址", "联系电话", "代理人", "代理人电话", "开户省份", "开户城市", "账号开户银行", "客户代码", "法人代表", "客户工商执照号码", "法人代表证件号码", "住宅地址", "邮政编码", "代办人证件号码", "邮箱地址", "关联资金账户", "地税纳税号", "单位电话", "代办人证件类型", "住宅电话", "法人代表证件类型", "国税纳税号", "单位地址", "工作单位", "销户网点", "最后交易时间", "账户销户银行", "任务流水号"]}, {"name": "人员信息", "keyWords": ["客户名称", "证照类型", "证照号码", "单位地址", "联系电话", "联系手机", "单位电话", "住宅电话", "工作单位", "邮箱地址", "代办人姓名", "代办人证件类型", "代办人证件号码", "国税纳税号", "地税纳税号", "法人代表", "法人代表证件类型", "法人代表证件号码", "出生日期", "户籍地址", "客户工商执照号码"]}]}, {"name": "支付宝", "OutputDir": "支付宝", "keyWords": ["交易号", "交易创建时间", "付款时间", "最近修改时间", "金额（元）", "收/支", "用户信息", "消费名称", "类型", "备注"]}, {"name": "微信", "OutputDir": "微信", "keyWords": ["用户ID", "用户银行卡号", "对手方ID", "对手方银行卡号", "交易单号", "交易时间", "交易金额(元)", "账户余额(元)", "借贷类型", "备注1", "备注2", "交易业务类型", "交易用途类型", "用户侧网银联单号", "网联/银联", "第三方账户名称", "对手侧网银联单号", "对手方接收时间", "对手方接收金额(元)", "大单号", "用户侧账号名称", "用户侧银行名称", "对手侧账户名称", "对手侧银行名称"]}, {"name": "人员信息", "OutputDir": "人员信息", "keyWords": ["客户名称", "证照类型", "证照号码", "单位地址", "联系电话", "联系手机", "单位电话", "住宅电话", "工作单位", "邮箱地址", "代办人姓名", "代办人证件类型", "代办人证件号码", "国税纳税号", "地税纳税号", "法人代表", "法人代表证件类型", "法人代表证件号码", "出生日期", "户籍地址", "客户工商执照号码"]}], "templates": [{"source": "支付宝", "mapping": {"交易卡号": "extract_account(用户信息)", "交易账号": "extract_account(用户信息)", "交易方户名": "extract_name(用户信息)", "交易方证件号码": "", "交易时间": "最近修改时间", "交易金额": "金额（元）", "交易余额": "", "收付标志": "normalize_income_expense(收/支)", "交易对手账卡号": "extract_account(交易对方信息)", "现金标志": "", "对手户名": "extract_name(交易对方信息)", "对手身份证号": "", "对手开户银行": "", "摘要说明": "消费名称", "交易币种": "CNY", "交易网点名称": "支付宝", "交易发生地": "交易来源地", "交易是否成功": "交易状态", "传票号": "", "IP地址": "", "MAC地址": "", "对手交易余额": "", "交易流水号": "交易号", "日志号": "", "凭证种类": "", "凭证号": "商户订单号", "交易柜员号": "", "备注": "备注", "商户名称": "", "交易类型": "类型", "查询反馈结果原因": "", "开户行": "支付宝", "文件路径": ""}, "transform": {"extract_name": "从字段中提取姓名", "extract_account": "从字段中提取账户", "latest": "从多个时间字段中选择最新的时间", "normalize_income_expense": "将收入转换为进,支出转换为出"}}, {"source": "银行_交易流水", "mapping": {"交易卡号": "normalize_card_number(交易卡号)", "交易账号": "normalize_card_number(交易账号)", "交易方户名": "交易方户名", "交易方证件号码": "交易方证件号码", "交易时间": "交易时间", "交易金额": "交易金额", "交易余额": "交易余额", "收付标志": "收付标志", "交易对手账卡号": "normalize_card_number(交易对手账卡号)", "现金标志": "现金标志", "对手户名": "对手户名", "对手身份证号": "对手身份证号", "对手开户银行": "对手开户银行", "摘要说明": "摘要说明", "交易币种": "交易币种", "交易网点名称": "交易网点名称", "交易发生地": "交易发生地", "交易是否成功": "交易是否成功", "传票号": "传票号", "IP地址": "IP地址", "MAC地址": "MAC地址", "对手交易余额": "对手交易余额", "交易流水号": "normalize_card_number(交易流水号)", "日志号": "日志号", "凭证种类": "凭证种类", "凭证号": "凭证号", "交易柜员号": "交易柜员号", "备注": "备注", "商户名称": "商户名称", "交易类型": "交易类型", "查询反馈结果原因": "查询反馈结果原因", "开户行": "账号开户银行", "文件路径": ""}, "transform": {"normalize_card_number": "将科学计数法表示的卡号转换为普通数字字符串"}}, {"source": "银行_账户信息", "mapping": {"账户开户名称": "账户开户名称", "开户人证件号码": "开户人证件号码", "交易卡号": "normalize_card_number(交易卡号)", "交易账号": "normalize_card_number(交易账号)", "账号开户时间": "账号开户时间", "账户余额": "账户余额", "可用余额": "可用余额", "币种": "币种", "开户网点代码": "开户网点代码", "开户网点": "开户网点", "账户状态": "账户状态", "钞汇标志名称": "钞汇标志名称", "开户人证件类型": "开户人证件类型", "销户日期": "销户日期", "账户类型": "账户类型", "开户联系方式": "开户联系方式", "通信地址": "通信地址", "联系电话": "联系电话", "代理人": "代理人", "代理人电话": "代理人电话", "备注": "备注", "开户省份": "开户省份", "开户城市": "开户城市", "账号开户银行": "账号开户银行", "客户代码": "客户代码", "法人代表": "法人代表", "客户工商执照号码": "客户工商执照号码", "法人代表证件号码": "法人代表证件号码", "住宅地址": "住宅地址", "邮政编码": "邮政编码", "代办人证件号码": "代办人证件号码", "邮箱地址": "邮箱地址", "关联资金账户": "关联资金账户", "地税纳税号": "地税纳税号", "单位电话": "单位电话", "代办人证件类型": "代办人证件类型", "住宅电话": "住宅电话", "法人代表证件类型": "法人代表证件类型", "国税纳税号": "国税纳税号", "单位地址": "单位地址", "工作单位": "工作单位", "销户网点": "销户网点", "最后交易时间": "最后交易时间", "账户销户银行": "账户销户银行", "任务流水号": "任务流水号", "文件名": "", "文件路径": ""}, "transform": {"normalize_date": "标准化日期格式为YYYY-MM-DD", "normalize_card_number": "将科学计数法表示的卡号转换为普通数字字符串"}}, {"source": "微信", "mapping": {"交易卡号": "用户ID", "交易账号": "用户ID", "交易方户名": "用户ID", "交易方证件号码": "", "交易时间": "交易时间", "交易金额": "normalize_currency(交易金额(分))", "交易余额": "normalize_currency(账户余额(分))", "收付标志": "normalize_income_expense(借贷类型)", "交易对手账卡号": "normalize_card_number(对手方银行卡号)", "现金标志": "", "对手户名": "对手方ID", "对手身份证号": "", "对手开户银行": "对手侧银行名称", "摘要说明": "concat(交易业务类型, 交易用途类型)", "交易币种": "CNY", "交易网点名称": "微信", "交易发生地": "", "交易是否成功": "", "传票号": "大单号", "IP地址": "", "MAC地址": "", "对手交易余额": "", "交易流水号": "交易单号", "日志号": "", "凭证种类": "", "凭证号": "用户侧网银联单号", "交易柜员号": "", "备注": "concat(备注1, 备注2)", "商户名称": "第三方账户名称", "交易类型": "微信", "查询反馈结果原因": "", "开户行": "用户侧银行名称", "文件路径": ""}, "transform": {"normalize_income_expense": "将 '入' 转换为 '进', '出' 转换为 '出'", "normalize_card_number": "将科学计数法表示的卡号转换为普通数字字符串", "normalize_currency": "将分转换为元", "concat": "合并多个字段为一个字符串"}}, {"source": "人员信息", "mapping": {"客户名称": "客户名称", "证照类型": "证照类型", "证照号码": "证照号码", "单位地址": "单位地址", "联系电话": "联系电话", "联系手机": "联系手机", "单位电话": "单位电话", "住宅电话": "住宅电话", "工作单位": "工作单位", "邮箱地址": "邮箱地址", "代办人姓名": "代办人姓名", "代办人证件类型": "代办人证件类型", "代办人证件号码": "代办人证件号码", "国税纳税号": "国税纳税号", "地税纳税号": "地税纳税号", "法人代表": "法人代表", "法人代表证件类型": "法人代表证件类型", "法人代表证件号码": "法人代表证件号码", "出生日期": "出生日期", "户籍地址": "户籍地址", "客户工商执照号码": "客户工商执照号码", "文件名": "", "文件路径": ""}, "transform": {}}, {"source": "未知", "mapping": {"交易卡号": "", "交易账号": "", "交易方户名": "", "交易方证件号码": "", "交易时间": "", "交易金额": "", "交易余额": "", "收付标志": "", "交易对手账卡号": "", "现金标志": "", "对手户名": "", "对手身份证号": "", "对手开户银行": "", "摘要说明": "", "交易币种": "", "交易网点名称": "", "交易发生地": "", "交易是否成功": "", "传票号": "", "IP地址": "", "MAC地址": "", "对手交易余额": "", "交易流水号": "", "日志号": "", "凭证种类": "", "凭证号": "", "交易柜员号": "", "备注": "", "商户名称": "", "交易类型": "未知", "查询反馈结果原因": "", "开户行": "", "文件路径": ""}, "transform": {}}, {"source": "银行_人员信息", "mapping": {"客户名称": "客户名称", "证照类型": "证照类型", "证照号码": "证照号码", "单位地址": "单位地址", "联系电话": "联系电话", "联系手机": "联系手机", "单位电话": "单位电话", "住宅电话": "住宅电话", "工作单位": "工作单位", "邮箱地址": "邮箱地址", "代办人姓名": "代办人姓名", "代办人证件类型": "代办人证件类型", "代办人证件号码": "代办人证件号码", "国税纳税号": "国税纳税号", "地税纳税号": "地税纳税号", "法人代表": "法人代表", "法人代表证件类型": "法人代表证件类型", "法人代表证件号码": "法人代表证件号码", "出生日期": "出生日期", "户籍地址": "户籍地址", "客户工商执照号码": "客户工商执照号码", "文件名": "", "文件路径": ""}, "transform": {}}]}