#!/bin/bash
# 设置 UTF-8 编码
export LANG=zh_CN.UTF-8

if [ "$1" == "" ]; then
    echo "错误：未提供输入目录参数"
    echo "用法: ./run.sh <输入目录>"
    echo "示例: ./run.sh ./input"
    exit 1
fi

echo "开始处理目录: $1"

# 创建logs目录（如果不存在）
mkdir -p logs

# 设置日志文件名（包含时间戳）
TIMESTAMP=$(date "+%Y%m%d_%H%M%S")
LOG_FILE="logs/process_$TIMESTAMP.log"

echo "日志将保存到: $LOG_FILE"

# 检测系统总内存 (以MB为单位)
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS 系统
    TOTAL_MEM_MB=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024)}')
else
    # Linux 系统
    TOTAL_MEM_MB=$(free -m | grep "Mem:" | awk '{print $2}')
fi

# 根据系统内存设置JVM和Spark内存
if [ $TOTAL_MEM_MB -gt 16000 ]; then
    # 16GB以上的系统
    DRIVER_MEM="8g"
    EXECUTOR_MEM="8g"
    MAX_HEAP="12g"
    INIT_HEAP="4g"
elif [ $TOTAL_MEM_MB -gt 8000 ]; then
    # 8GB-16GB的系统
    DRIVER_MEM="6g"
    EXECUTOR_MEM="6g" 
    MAX_HEAP="8g"
    INIT_HEAP="3g"
else
    # 8GB或更低的系统
    DRIVER_MEM="4g"
    EXECUTOR_MEM="4g"
    MAX_HEAP="4g"
    INIT_HEAP="2g"
fi

echo "系统总内存: ${TOTAL_MEM_MB}MB, 设置JVM最大堆内存: ${MAX_HEAP}"

java -Dfile.encoding=GB2312 \
-Xmx${MAX_HEAP} \
-Xms${INIT_HEAP} \
-XX:+UseG1GC \
-XX:+UseCompressedOops \
-XX:G1HeapRegionSize=32m \
-XX:+DisableExplicitGC \
-XX:+AlwaysPreTouch \
-Dspark.driver.memory=${DRIVER_MEM} \
-Dspark.executor.memory=${EXECUTOR_MEM} \
-Dspark.executor.cores=4 \
-Dspark.executor.instances=2 \
-Dspark.sql.adaptive.enabled=true \
-Dspark.driver.userClassPathFirst=true \
-Dspark.sql.adaptive.coalescePartitions.enabled=true \
-Dspark.memory.fraction=0.8 \
-Dspark.memory.storageFraction=0.3 \
-Dspark.local.dir=./tmp \
-Dspark.sql.files.maxPartitionBytes=134217728 \
-Dapp.quick.export=${QUICK_EXPORT} \
--add-opens=java.base/java.lang=ALL-UNNAMED \
--add-opens=java.base/java.lang.invoke=ALL-UNNAMED \
--add-opens=java.base/java.lang.reflect=ALL-UNNAMED \
--add-opens=java.base/java.io=ALL-UNNAMED \
--add-opens=java.base/java.net=ALL-UNNAMED \
--add-opens=java.base/java.nio=ALL-UNNAMED \
--add-opens=java.base/java.util=ALL-UNNAMED \
--add-opens=java.base/java.util.concurrent=ALL-UNNAMED \
--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED \
--add-opens=java.base/sun.nio.ch=ALL-UNNAMED \
--add-opens=java.base/sun.nio.cs=ALL-UNNAMED \
--add-opens=java.base/sun.security.action=ALL-UNNAMED \
--add-opens=java.base/sun.util.calendar=ALL-UNNAMED \
--add-exports=java.base/sun.nio.ch=ALL-UNNAMED \
--add-opens=java.management/sun.management=ALL-UNNAMED \
--add-opens=jdk.management/com.sun.management.internal=ALL-UNNAMED \
-jar "target/xiaowudemo-0.0.1-SNAPSHOT.jar" "$1" > "$LOG_FILE" 2>&1

exit_code=$?
if [ $exit_code -ne 0 ]; then
    echo "处理失败，请查看日志获取详细信息: $LOG_FILE"
    exit $exit_code
else
    echo "处理成功完成！"
    echo "输出结果保存在 ./output 目录"
    echo "详细日志保存在 $LOG_FILE"
fi 