@echo off
chcp 65001 > nul

if "%~1"=="" (
    echo 错误：未提供输入目录参数
    echo 用法: run.bat 输入目录
    echo 示例: run.bat input
    exit /b 1
)

echo 开始处理目录: %1

REM 创建logs目录和tmp目录
if not exist logs mkdir logs
if not exist tmp mkdir tmp

REM 设置日志文件名
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set LOG_FILE=logs\process_%TIMESTAMP%.log

echo 日志将保存到: %LOG_FILE%

REM 设置Hadoop环境变量
set HADOOP_HOME=%CD%\bin
set PATH=%HADOOP_HOME%;%PATH%
set HADOOP_CONF_DIR=%HADOOP_HOME%\etc\hadoop

REM 检查关键的 Hadoop 工具是否存在
if not exist "%HADOOP_HOME%\winutils.exe" (
    echo 错误：Hadoop 工具 'winutils.exe' 未在 '%HADOOP_HOME%' 目录中找到。
    echo 请确保正确版本的 'winutils.exe' 已放置在该目录下。
    exit /b 1
)

if not exist "%HADOOP_HOME%\hadoop.dll" (
    echo 错误：Hadoop 动态链接库 'hadoop.dll' 未在 '%HADOOP_HOME%' 目录中找到。
    echo 请确保正确版本的 'hadoop.dll' 已放置在该目录下。
    exit /b 1
)

REM 确保jar文件在正确位置
set JAR_FILE=%CD%\lib\xiaowudemo-0.0.1-SNAPSHOT-jar-with-dependencies.jar

REM 直接用java命令运行jar包
java -Dfile.encoding=GB2312 ^
-Dhadoop.home.dir="%HADOOP_HOME%" ^
-Xmx4g ^
-Xms512m ^
-XX:+UseG1GC ^
-XX:+UseCompressedOops ^
-XX:G1HeapRegionSize=32m ^
-XX:+DisableExplicitGC ^
-Dspark.driver.memory=4g ^
-Dspark.executor.memory=4g ^
-Dspark.local.dir=./tmp ^
--add-opens=java.base/java.lang=ALL-UNNAMED ^
--add-opens=java.base/java.lang.invoke=ALL-UNNAMED ^
--add-opens=java.base/java.lang.reflect=ALL-UNNAMED ^
--add-opens=java.base/java.io=ALL-UNNAMED ^
--add-opens=java.base/java.net=ALL-UNNAMED ^
--add-opens=java.base/java.nio=ALL-UNNAMED ^
--add-opens=java.base/java.util=ALL-UNNAMED ^
--add-opens=java.base/java.util.concurrent=ALL-UNNAMED ^
--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED ^
--add-opens=java.base/sun.nio.ch=ALL-UNNAMED ^
--add-opens=java.base/sun.nio.cs=ALL-UNNAMED ^
--add-opens=java.base/sun.security.action=ALL-UNNAMED ^
--add-opens=java.base/sun.util.calendar=ALL-UNNAMED ^
--add-exports=java.base/sun.nio.ch=ALL-UNNAMED ^
--add-opens=java.management/sun.management=ALL-UNNAMED ^
--add-opens=jdk.management/com.sun.management.internal=ALL-UNNAMED ^
-jar "%JAR_FILE%" "%~1" > "%LOG_FILE%" 2>&1

if %ERRORLEVEL% neq 0 (
    echo 处理失败，请查看日志获取详细信息: %LOG_FILE%
    exit /b %ERRORLEVEL%
) else (
    echo 处理成功完成！
    echo 输出结果保存在 .\output 目录
    echo 详细日志保存在 %LOG_FILE%
)