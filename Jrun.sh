   #!/bin/bash
   # 设置 UTF-8 编码
   export LANG=zh_CN.UTF-8
   
   # JProfiler安装路径 - 请根据实际路径调整
   JPROFILER_PATH="/Applications/JProfiler.app/Contents/Resources/app"
   
   if [ "$1" == "" ]; then
       echo "错误：未提供输入目录参数"
       echo "用法: ./run.sh <输入目录>"
       echo "示例: ./run.sh ./input"
       exit 1
   fi
   
   echo "开始处理目录: $1"
   
   # 创建logs目录（如果不存在）
   mkdir -p logs
   
   # 设置日志文件名（包含时间戳）
   TIMESTAMP=$(date "+%Y%m%d_%H%M%S")
   LOG_FILE="logs/process_$TIMESTAMP.log"
   
   echo "日志将保存到: $LOG_FILE"
   
   # 启动应用并附加JProfiler代理
   java -Dfile.encoding=GB2312 \
   -XX:+UseG1GC \
   -XX:+UseCompressedOops \
   -Dspark.executor.cores=4 \
   -Dspark.executor.instances=2 \
   -Dspark.sql.adaptive.enabled=true \
   -Dspark.sql.adaptive.coalescePartitions.enabled=true \
   -agentpath:$JPROFILER_PATH/bin/macos/libjprofilerti.jnilib=port=8849,nowait \
   --add-opens=java.base/java.lang=ALL-UNNAMED \
   --add-opens=java.base/java.lang.invoke=ALL-UNNAMED \
   --add-opens=java.base/java.lang.reflect=ALL-UNNAMED \
   --add-opens=java.base/java.io=ALL-UNNAMED \
   --add-opens=java.base/java.net=ALL-UNNAMED \
   --add-opens=java.base/java.nio=ALL-UNNAMED \
   --add-opens=java.base/java.util=ALL-UNNAMED \
   --add-opens=java.base/java.util.concurrent=ALL-UNNAMED \
   --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED \
   --add-opens=java.base/sun.nio.ch=ALL-UNNAMED \
   --add-opens=java.base/sun.nio.cs=ALL-UNNAMED \
   --add-opens=java.base/sun.security.action=ALL-UNNAMED \
   --add-opens=java.base/sun.util.calendar=ALL-UNNAMED \
   --add-exports=java.base/sun.nio.ch=ALL-UNNAMED \
   --add-opens=java.management/sun.management=ALL-UNNAMED \
   --add-opens=jdk.management/com.sun.management.internal=ALL-UNNAMED \
   -jar "target/xiaowudemo-0.0.1-SNAPSHOT.jar" "$1" > "$LOG_FILE" 2>&1 &
   
   echo $! > ./logs/app.pid
   echo "应用已启动，进程ID: $(cat ./logs/app.pid)"
   echo "JProfiler可以通过端口8849连接"